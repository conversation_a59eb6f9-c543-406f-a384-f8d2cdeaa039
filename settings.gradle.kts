rootProject.name = "Memory"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    includeBuild("build-logic/convention")
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
    }
}

include(":composeApp")
include(":logging")
include(":kotlinUtils")
include(":domain:timeline")
include(":domain:authentication")
include(":data:network")
include(":data:persistence")
include(":data:repository")
include(":di")
include(":ui:common")
include(":ui:desktop-webview")
include(":ui:root")
include(":ui:settings")
include(":ui:signed-in")
include(":ui:signed-out")
include(":ui:timeline")
include(":ui:landing")
include(":navigation")
include(":ui:serverselect")
include(":fedidb")
include(":domain:serverlist")
include(":mastodon")
include(":domain:login")
include(":domain:register")
include(":ui:createAccount")
