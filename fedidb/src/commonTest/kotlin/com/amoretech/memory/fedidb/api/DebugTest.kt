package com.amoretech.memory.fedidb.api

import app.cash.turbine.test
import com.amoretech.memory.kotlinUtils.ApiResult
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json

class DebugTest : BehaviorSpec({
    given("Debug test") {
        `when`("testing invalid JSON") {
            val engine = MockEngine {
                respond(
                    content = ByteReadChannel("INVALID_JSON"),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
            }
            val api = FedIDBApi(
                httpClient = HttpClient(engine) {
                    install(ContentNegotiation) {
                        json(Json { ignoreUnknownKeys = true; encodeDefaults = true })
                    }
                }
            )

            then("should emit parsing error") {
                runTest {
                    api.getInstances().test {
                        val result = awaitItem()
                        println("Actual result: $result")
                        
                        println("Result type: ${result::class.simpleName}")
                        when (result) {
                            is ApiResult.Error -> {
                                println("Error message: '${result.message}'")
                                println("Error exception: ${result.exception}")
                            }
                            is ApiResult.Success -> {
                                println("Success data: ${result.data}")
                            }
                            is ApiResult.Loading -> {
                                println("Loading state")
                            }
                        }

                        result.shouldBeInstanceOf<ApiResult.Error>()
                        val error = result as ApiResult.Error
                        error.message shouldContain "Data parsing error for successful response"
                        
                        awaitComplete()
                    }
                }
            }
        }
    }
})
