package com.amoretech.memory.fedidb.api

import app.cash.turbine.test
import com.amoretech.memory.fedidb.dto.FedIDBInstance
import com.amoretech.memory.fedidb.dto.Location
import com.amoretech.memory.fedidb.dto.Software
import com.amoretech.memory.fedidb.dto.Stats
import com.amoretech.memory.fedidb.dto.TestFedIDBInstance
import com.amoretech.memory.fedidb.dto.TestFedIDBResponse
import com.amoretech.memory.fedidb.dto.TestFedIDBStats
import com.amoretech.memory.fedidb.dto.TestLinks
import com.amoretech.memory.fedidb.dto.TestMeta
import com.amoretech.memory.fedidb.dto.fedIDBInstance
import com.amoretech.memory.fedidb.dto.testLinks
import com.amoretech.memory.fedidb.dto.testMeta
import com.amoretech.memory.kotlinUtils.ApiResult
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldStartWith
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.property.Arb
import io.kotest.property.arbitrary.int
import io.kotest.property.arbitrary.list
import io.kotest.property.checkAll
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.request
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.test.runTest
import kotlinx.io.IOException
import kotlinx.serialization.json.Json

open class FedIDBApiTest :
    BehaviorSpec({
        val testInstances =
            listOf(
                TestFedIDBInstance(
                    id = "1",
                    domain = "mastodon.social",
                    country = "US",
                    url = "https://mastodon.social",
                    users = 100000,
                    open_registration = true,
                    description = "A friendly Mastodon instance",
                    banner_url = null,
                    location = Location(city = null, country = "USA"),
                    software = Software(name = "Mastodon", version = "4.2.1", slug = "mastodon"),
                    stats =
                        TestFedIDBStats(
                            user_count = 100000,
                            status_count = 5000000,
                            domain_count = 1,
                        ),
                    first_seen_at = "2017-01-01T00:00:00Z",
                    last_seen_at = "2024-07-02T11:30:00Z",
                ),
                TestFedIDBInstance(
                    id = "2",
                    domain = "infosec.exchange",
                    country = "DE",
                    url = "https://infosec.exchange",
                    users = 50000,
                    open_registration = false,
                    description = "Infosec-focused instance",
                    banner_url = "https://example.com/banner.png",
                    location = Location(city = null, country = "Germany"),
                    software = Software(name = "Mastodon", version = "4.1.0", slug = "mastodon"),
                    stats =
                        TestFedIDBStats(
                            user_count = 50000,
                            status_count = 2000000,
                            domain_count = 1,
                        ),
                    first_seen_at = "2018-05-15T00:00:00Z",
                    last_seen_at = "2024-07-02T11:30:00Z",
                ),
            )

        val testResponse =
            TestFedIDBResponse(
                data = testInstances,
                links =
                    TestLinks(
                        first = "http://example.com/api/v1/instances?page=1",
                        last = "http://example.com/api/v1/instances?page=5",
                        prev = "",
                        next = "http://example.com/api/v1/instances?page=2",
                    ),
                meta =
                    TestMeta(
                        path = "http://example.com/api/v1/instances",
                        perPage = 2,
                        nextCursor = "someNextCursorValue",
                        prevCursor = null,
                    ),
            )

        fun createApiWithEngine(engine: MockEngine) =
            FedIDBApi(
                httpClient =
                    HttpClient(engine) {
                        install(ContentNegotiation) {
                            json(
                                Json {
                                    ignoreUnknownKeys = true
                                    encodeDefaults = true
                                },
                            )
                        }
                    },
            )

        given("FedIDBApi") {
            `when`("requesting instances with valid response") {
                val engine =
                    MockEngine {
                        respond(
                            content = ByteReadChannel(Json.encodeToString(testResponse)),
                            status = HttpStatusCode.OK,
                            headers = headersOf(HttpHeaders.ContentType, "application/json"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit success with instances") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Success<List<FedIDBInstance>>>()
                            val success = result as ApiResult.Success<List<FedIDBInstance>>
                            val expectedFedIDBInstances =
                                testInstances.map { testInstance ->
                                    // This is a placeholder. You'll need to define how to convert
                                    // TestFedIDBInstance to FedIDBInstance, matching fields.
                                    // For example:
                                    FedIDBInstance(
                                        id = testInstance.id.toLong(),
                                        domain = testInstance.domain,
                                        // ... map all other fields, paying attention to name differences like
                                        // open_registration vs openRegistration, banner_url vs bannerUrl,
                                        // TestFedIDBStats vs Stats, country, url, users, etc.
                                        // Also, handle any missing fields in TestFedIDBInstance that are in FedIDBInstance (like monthlyActiveUsers)
                                        // or vice versa, ensuring your test data fully represents what the actual DTO contains.
                                        // For example, if FedIDBInstance.stats has 'monthlyActiveUsers' but TestFedIDBStats doesn't,
                                        // you'll need to account for that (e.g., provide a default or add it to TestFedIDBStats).
                                        openRegistration = testInstance.open_registration,
                                        description = testInstance.description,
                                        bannerUrl = testInstance.banner_url,
                                        location = testInstance.location!!,
                                        software = testInstance.software!!,
                                        firstSeenAt = testInstance.first_seen_at,
                                        lastSeenAt = testInstance.last_seen_at,
                                        // For stats, you'll need a similar conversion:
                                        stats =
                                            Stats(
                                                userCount = testInstance.stats.user_count.toLong(),
                                                statusCount = testInstance.stats.status_count.toLong(),
                                                // Add default or map the new field if it exists in TestFedIDBStats
                                                monthlyActiveUsers = 0, // Assuming a default or that this isn't in TestFedIDBStats
                                            ),
                                    )
                                }
                            success.data.shouldContainExactly(expectedFedIDBInstances)

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with client error") {
                val engine =
                    MockEngine {
                        respondError(
                            status = HttpStatusCode.BadRequest,
                            content = "Bad Request",
                            headers = headersOf(HttpHeaders.ContentType, "text/plain"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit client error with detailed message") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error

                            error.message shouldBe "Client error: Bad Request - Bad Request"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with network error") {
                val engine =
                    MockEngine {
                        throw IOException("Network unavailable")
                    }
                val api = createApiWithEngine(engine)

                then("it should emit network error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Network error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("using different limit values") {
                then("it should pass correct limit parameter") {
                    runTest {
                        // Property test with Kotest
                        checkAll(Arb.int(1..100)) { limit ->
                            val engine =
                                MockEngine { request ->
                                    val paramLimit = request.url.parameters["limit"]?.toInt() ?: 10
                                    paramLimit shouldBe limit

                                    respond(Json.encodeToString(testResponse))
                                }
                            val api = createApiWithEngine(engine)

                            api.getInstances(limit).test {
                                awaitItem()
                                awaitComplete()
                            }
                        }
                    }
                }
            }

            `when`("receiving invalid JSON response") {
                val engine =
                    MockEngine {
                        respond(
                            content = ByteReadChannel("INVALID_JSON"),
                            status = HttpStatusCode.OK,
                            headers = headersOf(HttpHeaders.ContentType, "application/json"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit parsing error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldStartWith "An unexpected error occurred:"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with property-based data") {
                then("it should handle various instance lists") {
                    // Property test with generated data
                    runTest {
                        checkAll(
                            Arb.list(Arb.fedIDBInstance(), range = 1..5), // Reduced range for faster tests
                            Arb.testLinks(),
                            Arb.testMeta(),
                        ) { instances, links, meta ->
                            // Filter out instances with null location or software for the response
                            val validInstances = instances.filter { it.location != null && it.software != null }
                            val engine =
                                MockEngine {
                                    respond(
                                        Json
                                            .encodeToString(
                                                TestFedIDBResponse(
                                                    validInstances,
                                                    links,
                                                    meta,
                                                ),
                                            ),
                                        status = HttpStatusCode.OK,
                                        headers = headersOf(HttpHeaders.ContentType, "application/json"),
                                    )
                                }
                            val api = createApiWithEngine(engine)

                            api.getInstances().test {
                                val result = awaitItem()

                                // Kotest assertions only
                                result.shouldBeInstanceOf<ApiResult.Success<List<FedIDBInstance>>>()
                                val success = result as ApiResult.Success<List<FedIDBInstance>>

                                // Convert TestFedIDBInstance to FedIDBInstance for comparison
                                // Filter out instances with null location or software since FedIDBInstance requires non-null values
                                val validInstances = instances.filter { it.location != null && it.software != null }
                                val expectedInstances = validInstances.map { testInstance ->
                                    FedIDBInstance(
                                        id = testInstance.id.toLong(),
                                        domain = testInstance.domain,
                                        openRegistration = testInstance.open_registration,
                                        description = testInstance.description,
                                        bannerUrl = testInstance.banner_url,
                                        location = testInstance.location!!,
                                        software = testInstance.software!!,
                                        firstSeenAt = testInstance.first_seen_at,
                                        lastSeenAt = testInstance.last_seen_at,
                                        stats = Stats(
                                            userCount = testInstance.stats.user_count.toLong(),
                                            statusCount = testInstance.stats.status_count.toLong(),
                                            monthlyActiveUsers = 0, // Default value
                                        ),
                                    )
                                }
                                success.data.shouldContainExactly(expectedInstances)

                                awaitComplete()
                            }
                        }
                    }
                }
            }

            `when`("requesting instances with server error") {
                val engine =
                    MockEngine {
                        respondError(
                            status = HttpStatusCode.InternalServerError,
                            content = "Internal Server Error",
                            headers = headersOf(HttpHeaders.ContentType, "text/plain"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit server error with detailed message") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldBe "Server error: Internal Server Error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with redirection") {
                val engine =
                    MockEngine {
                        respond(
                            content = "Moved Permanently",
                            status = HttpStatusCode.MovedPermanently,
                            headers = headersOf(HttpHeaders.ContentType, "text/plain"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit redirection error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldStartWith "An unexpected error occurred:"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with empty response") {
                val emptyResponse =
                    TestFedIDBResponse(
                        data = emptyList(),
                        links = TestLinks(),
                        meta = TestMeta(path = "", perPage = 0, nextCursor = null, prevCursor = null),
                    )
                val engine =
                    MockEngine {
                        respond(
                            content = ByteReadChannel(Json.encodeToString(emptyResponse)),
                            status = HttpStatusCode.OK,
                            headers = headersOf(HttpHeaders.ContentType, "application/json"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit success with empty list") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Success<List<FedIDBInstance>>>()
                            val success = result as ApiResult.Success<List<FedIDBInstance>>
                            success.data shouldBe emptyList()

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("using edge case limit values") {
                then("it should handle minimum limit") {
                    runTest {
                        val engine =
                            MockEngine { request ->
                                val paramLimit = request.url.parameters["limit"]?.toInt() ?: 10
                                paramLimit shouldBe 1

                                respond(Json.encodeToString(testResponse))
                            }
                        val api = createApiWithEngine(engine)

                        api.getInstances(1).test {
                            awaitItem()
                            awaitComplete()
                        }
                    }
                }

                then("it should handle maximum reasonable limit") {
                    runTest {
                        val engine =
                            MockEngine { request ->
                                val paramLimit = request.url.parameters["limit"]?.toInt() ?: 10
                                paramLimit shouldBe 1000

                                respond(Json.encodeToString(testResponse))
                            }
                        val api = createApiWithEngine(engine)

                        api.getInstances(1000).test {
                            awaitItem()
                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with malformed JSON") {
                val engine =
                    MockEngine {
                        respond(
                            content = ByteReadChannel("{\"data\": [malformed json}"),
                            status = HttpStatusCode.OK,
                            headers = headersOf(HttpHeaders.ContentType, "application/json"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit parsing error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldStartWith "An unexpected error occurred:"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with unexpected status code") {
                val engine =
                    MockEngine {
                        respond(
                            content = "Unknown error",
                            status = HttpStatusCode(999, "Unknown"),
                            headers = headersOf(HttpHeaders.ContentType, "text/plain"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit generic HTTP error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldBe "HTTP Error: 999 Unknown - Unknown error"

                            awaitComplete()
                        }
                    }
                }
            }
        }


    })
