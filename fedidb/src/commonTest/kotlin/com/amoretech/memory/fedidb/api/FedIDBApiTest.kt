package com.amoretech.memory.fedidb.api

import app.cash.turbine.test
import com.amoretech.memory.fedidb.dto.TestFedIDBInstance
import com.amoretech.memory.fedidb.dto.TestFedIDBResponse
import com.amoretech.memory.kotlinUtils.ApiResult
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.property.Arb
import io.kotest.property.arbitrary.bind
import io.kotest.property.arbitrary.int
import io.kotest.property.arbitrary.list
import io.kotest.property.arbitrary.string
import io.kotest.property.checkAll
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.request
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.test.runTest
import kotlinx.io.IOException
import kotlinx.serialization.json.Json

open class FedIDBApiTest :
    BehaviorSpec({
        val testInstances =
            listOf(
                TestFedIDBInstance("1", "mastodon.social", "US", "https://mastodon.social", 100000),
                TestFedIDBInstance("2", "infosec.exchange", "DE", "https://infosec.exchange", 50000),
            )

        val testResponse = TestFedIDBResponse(data = testInstances)

        fun createApiWithEngine(engine: MockEngine) =
            FedIDBApi(
                httpClient =
                    HttpClient(engine) {
                        install(ContentNegotiation) {
                            json(Json { ignoreUnknownKeys = true })
                        }
                    },
            )

        given("FedIDBApi") {
            `when`("requesting instances with valid response") {
                val engine =
                    MockEngine {
                        respond(
                            content = ByteReadChannel(Json.encodeToString(testResponse)),
                            status = HttpStatusCode.OK,
                            headers = headersOf(HttpHeaders.ContentType, "application/json"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit success with instances") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Success<List<TestFedIDBInstance>>>()
                            val success = result as ApiResult.Success
                            success.data.shouldContainExactly(testInstances)

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with client error") {
                val engine =
                    MockEngine {
                        respondError(
                            status = HttpStatusCode.BadRequest,
                            content = "Bad Request",
                            headers = headersOf(HttpHeaders.ContentType, "text/plain"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit client error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldBe "Client error: Bad Request"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with network error") {
                val engine =
                    MockEngine {
                        throw IOException("Network unavailable")
                    }
                val api = createApiWithEngine(engine)

                then("it should emit network error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Network error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("using different limit values") {
                then("it should pass correct limit parameter") {
                    runTest {
                        // Property test with Kotest
                        checkAll(Arb.int(1..100)) { limit ->
                            val engine =
                                MockEngine { request ->
                                    val paramLimit = request.url.parameters["limit"]?.toInt() ?: 10
                                    paramLimit shouldBe limit

                                    respond(Json.encodeToString(testResponse))
                                }
                            val api = createApiWithEngine(engine)

                            api.getInstances(limit).test {
                                awaitItem()
                                awaitComplete()
                            }
                        }
                    }
                }
            }

            `when`("receiving invalid JSON response") {
                val engine =
                    MockEngine {
                        respond(
                            content = ByteReadChannel("INVALID_JSON"),
                            status = HttpStatusCode.OK,
                            headers = headersOf(HttpHeaders.ContentType, "application/json"),
                        )
                    }
                val api = createApiWithEngine(engine)

                then("it should emit parsing error") {
                    runTest {
                        api.getInstances().test {
                            val result = awaitItem()

                            // Kotest assertions only
                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Data parsing error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("requesting instances with property-based data") {
                then("it should handle various instance lists") {
                    // Property test with generated data
                    runTest {
                        checkAll(Arb.list(Arb.fedIDBInstance(), range = 1..100)) { instances ->
                            val engine =
                                MockEngine {
                                    respond(Json.encodeToString(TestFedIDBResponse(instances)))
                                }
                            val api = createApiWithEngine(engine)

                            api.getInstances().test {
                                val result = awaitItem()

                                // Kotest assertions only
                                result.shouldBeInstanceOf<ApiResult.Success<List<TestFedIDBInstance>>>()
                                val success = result as ApiResult.Success
                                success.data.shouldContainExactly(instances)

                                awaitComplete()
                            }
                        }
                    }
                }
            }
        }
    }) {
    companion object {
        // Custom Arbitrary for generating test instances
        fun Arb.Companion.fedIDBInstance(): Arb<TestFedIDBInstance> =
            Arb.bind(
                Arb.string(),
                Arb.string(),
                Arb.string(),
                Arb.string(),
                Arb.int(1..1000000),
            ) { id, domain, country, url, users ->
                TestFedIDBInstance(id, domain, country, url, users)
            }
    }
}
