package com.amoretech.memory.fedidb.dto

import io.kotest.property.Arb
import io.kotest.property.arbitrary.bind
import io.kotest.property.arbitrary.int
import io.kotest.property.arbitrary.string
import kotlinx.serialization.Serializable

// Custom Arbitrary for generating test instances
fun Arb.Companion.fedIDBInstance(): Arb<TestFedIDBInstance> =
    Arb.bind(
        Arb.string(),
        Arb.string(),
        Arb.string(),
        Arb.string(),
        Arb.int(1..1000000),
    ) { id, domain, country, url, users ->
        TestFedIDBInstance(id, domain, country, url, users)
    }

// Test data classes
@kotlinx.serialization.Serializable
data class TestFedIDBResponse(
    val data: List<TestFedIDBInstance>,
)

@Serializable
data class TestFedIDBInstance(
    val id: String,
    val domain: String,
    val country: String,
    val url: String,
    val users: Int,
)
