package com.amoretech.memory.fedidb.dto

import io.kotest.property.Arb
import io.kotest.property.arbitrary.bind
import io.kotest.property.arbitrary.boolean
import io.kotest.property.arbitrary.int
import io.kotest.property.arbitrary.orNull
import io.kotest.property.arbitrary.string
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// Custom Arbitrary for generating test instances
fun Arb.Companion.fedIDBInstance(): Arb<TestFedIDBInstance> =
    Arb.bind(
        Arb.long(1..999999999).map { it.toString() }, // Generate numeric string IDs
        Arb.string(),
        Arb.string(),
        Arb.string(),
        Arb.int(1..1000000),
        Arb.boolean(),
        Arb.string(),
        Arb.string().orNull(),
        Arb.location().orNull(),
        Arb.software(),
        Arb.testFedIDBStats(),
        Arb.string(),
        Arb.string(),
    ) { id, domain, country, url, users, openReg, desc, banner, loc, software, stats, firstSeen, lastSeen ->
        TestFedIDBInstance(id, domain, country, url, users, openReg, desc, banner, loc, software, stats, firstSeen, lastSeen)
    }

fun Arb.Companion.testFedIDBStats(): Arb<TestFedIDBStats> =
    Arb.bind(
        Arb.int(0..1000000),
        Arb.int(0..5000000),
        Arb.int(0..10000),
    ) { users, statuses, domains ->
        TestFedIDBStats(users, statuses, domains)
    }

fun Arb.Companion.software(): Arb<Software> =
    Arb.bind(
        Arb.int().orNull(),
        Arb.string().orNull(),
        Arb.string().orNull(),
        Arb.string(),
        Arb.string(),
    ) { id, name, url, version, slug ->
        Software(id, name, url, version, slug)
    }

@Serializable
data class TestLinks(
    val first: String? = null,
    val last: String? = null,
    val prev: String? = null,
    val next: String? = null,
)

@Serializable
data class TestMeta(
    val path: String,
    @SerialName("per_page")
    val perPage: Int,
    @SerialName("next_cursor")
    val nextCursor: String?,
    @SerialName("prev_cursor")
    val prevCursor: String?,
)

fun Arb.Companion.testLinks(): Arb<TestLinks> =
    Arb.bind(
        Arb.string().orNull(), // first
        Arb.string().orNull(), // last
        Arb.string(), // prev
        Arb.string().orNull(), // next
    ) { first, last, prev, next ->
        TestLinks(first, last, prev, next)
    }

fun Arb.Companion.testMeta(): Arb<TestMeta> =
    Arb.bind(
        Arb.string(), // path (non-nullable)
        Arb.int(1..50), // perPage (non-nullable)
        Arb.string().orNull(), // nextCursor (nullable)
        Arb.string().orNull(), // prevCursor (nullable)
    ) { path, perPage, nextCursor, prevCursor ->
        TestMeta(path, perPage, nextCursor, prevCursor)
    }

// Test data classes
@kotlinx.serialization.Serializable
data class TestFedIDBResponse(
    val data: List<TestFedIDBInstance>,
    val links: TestLinks,
    val meta: TestMeta,
)

@Serializable
data class TestFedIDBInstance(
    val id: String,
    val domain: String,
    val country: String,
    val url: String,
    val users: Int,
    val open_registration: Boolean,
    val description: String,
    val banner_url: String?,
    val location: Location?,
    val software: Software?,
    val stats: TestFedIDBStats,
    val first_seen_at: String,
    val last_seen_at: String,
)

@Serializable
data class TestFedIDBStats(
    val user_count: Int,
    val status_count: Int,
    val domain_count: Int,
)

fun Arb.Companion.location(): Arb<Location> =
    Arb.bind(
        Arb.string().orNull(),
        Arb.string().orNull(),
    ) { city, country ->
        Location(city, country)
    }
