package com.amoretech.memory.fedidb.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FedIDBInstance(
    val id: Long,
    val domain: String,
    @SerialName("open_registration")
    val openRegistration: Boolean,
    val description: String?,
    @SerialName("banner_url")
    val bannerUrl: String?,
    val location: Location,
    val software: Software,
    val stats: Stats,
    @SerialName("first_seen_at")
    val firstSeenAt: String,
    @SerialName("last_seen_at")
    val lastSeenAt: String,
)
