package com.amoretech.memory.fedidb.api

import com.amoretech.memory.fedidb.constant.FedidbConstant
import com.amoretech.memory.fedidb.dto.FedIDBInstance
import com.amoretech.memory.fedidb.dto.FedIDBResponse
import com.amoretech.memory.kotlinUtils.ApiResult
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.RedirectResponseException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.request.get
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.io.IOException
import kotlinx.serialization.SerializationException

class FedIDBApi(
    private val httpClient: HttpClient,
) {
    fun getInstances(limit: Int = 10): Flow<ApiResult<List<FedIDBInstance>>> =
        flow {
            val urlString = "${FedidbConstant.BASE_URL}${FedidbConstant.SERVERS_ENDPOINT}?limit=$limit"
            try {
                val response: FedIDBResponse = httpClient.get(urlString).body()
                emit(ApiResult.Success(response.data))
            } catch (e: RedirectResponseException) {
                // 3xx - Redirection
                emit(ApiResult.Error("Redirection error: ${e.response.status.description}", e))
            } catch (e: ClientRequestException) {
                // 4xx - Client errors (e.g., BadRequest, Unauthorized, NotFound)
                emit(ApiResult.Error("Client error: ${e.response.status.description}", e))
            } catch (e: ServerResponseException) {
                // 5xx - Server errors
                emit(ApiResult.Error("Server error: ${e.response.status.description}", e))
            } catch (e: IOException) {
                // Network connectivity issues
                emit(ApiResult.Error("Network error: ${e.message ?: "Unknown network error"}", e))
            } catch (e: SerializationException) {
                // Error deserializing the JSON response
                emit(ApiResult.Error("Data parsing error: ${e.message ?: "Could not parse server response"}", e))
            } catch (e: Exception) {
                // Catch-all for any other unexpected errors
                emit(ApiResult.Error("An unexpected error occurred: ${e.message ?: "Unknown error"}", e))
            }
        }.flowOn(Dispatchers.IO) // To execute the network call and emission on an I/O thread
            .catch { e ->
                // This secondary catch is for unexpected errors within the flow itself
                emit(ApiResult.Error("An unexpected error occurred in the flow: ${e.message ?: "Unknown flow error"}", e as? Exception))
            }
}
