package com.amoretech.memory.di

import com.amoretech.memory.data.network.di.networkModule
import com.amoretech.memory.data.persistence.di.persistenceModule
import com.amoretech.memory.data.repository.di.repositoryModule
import com.amoretech.memory.data.repository.timeline.timelineRepoModule
import com.amoretech.memory.domain.authentication.di.domainAuthModule
import com.amoretech.memory.logging.loggingModule
import com.amoretech.memory.login.di.domainLoginModule
import com.amoretech.memory.register.di.domainRegisterModule
import com.amoretech.memory.serverlist.di.domainServerListModule

/**
 * Base Koin module shared across all apps (android, iOS, Desktop)
 */
fun appModule() =
    listOf(
        // platformModule,
        networkModule,
        persistenceModule,
        domainAuthModule,
        domainServerListModule,
        domainLoginModule,
        domainRegisterModule,
        repositoryModule,
        timelineRepoModule,
        viewModelModule,
        loggingModule,
    )
