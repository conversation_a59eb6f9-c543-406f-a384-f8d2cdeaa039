package com.amoretech.memory.di

import com.amoretech.memory.serverselect.viewmodel.SelectServerViewModel
import com.amoretech.memory.ui.root.root.viewmodel.RootViewModel
import com.amoretech.memory.ui.settings.SettingsViewModel
import com.amoretech.memory.ui.signedIn.SignInViewModel
import com.amoretech.memory.ui.timeline.TimelineViewModel
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin module containing all ViewModel definitions
 */
val viewModelModule: Module =
    module {

        factory {
            RootViewModel()
        }

        factory {
            TimelineViewModel()
        }

        factory {
            SelectServerViewModel()
        }

        factory {
            SignInViewModel()
        }

        factory {
            SettingsViewModel()
        }
    }
