import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    id("detekt")
    id("ktlint")
}

kotlin {

    androidLibrary {
        namespace = "com.amoretech.memory.di"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    val xcfName = "diKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(libs.kotlin.stdlib)
                implementation(projects.data.network)
                implementation(projects.data.persistence)
                implementation(projects.data.repository)
                implementation(projects.logging)
                implementation(projects.domain.authentication)
                implementation(projects.domain.serverlist)
                implementation(projects.domain.login)
                implementation(projects.domain.register)
                implementation(projects.ui.root)
                implementation(projects.ui.timeline)
                implementation(projects.ui.signedIn)
                implementation(projects.ui.signedOut)
                implementation(projects.ui.settings)
                implementation(projects.ui.serverselect)
                implementation(projects.ui.createAccount)
                implementation(projects.ui.desktopWebview)
                implementation(libs.io.insert.koin.core)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
            }
        }

        androidMain {
            dependencies {}
        }

        iosMain {
            dependencies {}
        }

        jvmMain {
            dependencies {}
        }

        jvmTest {
            dependencies {}
        }
    }
}
