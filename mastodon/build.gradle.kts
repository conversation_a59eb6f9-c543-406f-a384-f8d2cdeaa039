import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.ksp)
    alias(libs.plugins.room)
    id("detekt")
    id("ktlint")
}

kotlin {

    jvmToolchain(17)

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.mastodon"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "mastodonKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(libs.kotlin.stdlib)
                implementation(libs.kotlinx.coroutines.core)
                implementation(libs.io.ktor.client.core)
                implementation(libs.io.ktor.client.content.negotiation)
                implementation(libs.io.ktor.serialization.kotlinx.json)
                implementation(libs.io.insert.koin.core)
                implementation(libs.kotlinx.serialization.json)
                implementation(projects.kotlinUtils)
                implementation(projects.logging)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
                implementation(libs.kotest.framework.engine)
                implementation(libs.kotest.assertions.core)
                implementation(libs.kotest.property)
                implementation(libs.org.jetbrains.kotlinx.coroutines.test)
                implementation(libs.turbine)
                implementation(libs.io.ktor.client.mock)
                implementation(libs.io.ktor.serialization.kotlinx.json)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.io.ktor.client.okhttp)
            }
        }

        iosMain {
            dependencies {
                implementation(libs.io.ktor.client.darwin)
            }
        }

        jvmMain {
            dependencies {
                implementation(libs.io.ktor.client.cio)
            }
        }

        jvmTest {
            dependencies {
                implementation(libs.kotest.runner.junit5)
                implementation(kotlin("test-junit5"))
                // exclude junit4 to avoid conflict
                configurations.all {
                    exclude(group = "org.jetbrains.kotlin", module = "kotlin-test-junit")
                }
                // Truth assertions (JVM only)
                implementation(libs.truth)
                // JUnit 5 (for JVM/Android tests)
                implementation(libs.junit.jupiter.api)
                implementation(libs.junit.jupiter.engine)
            }
        }
    }
}
