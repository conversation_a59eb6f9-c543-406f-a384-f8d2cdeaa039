import app.cash.turbine.test
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.mastodon.api.MastodonApi
import com.amoretech.memory.mastodon.constant.MastodonConstant
import com.amoretech.memory.mastodon.dto.*
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.plugins.timeout
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.forms.FormDataContent
import io.ktor.http.*
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlin.test.assertIs
import kotlin.test.assertContains

@OptIn(ExperimentalCoroutinesApi::class)
class MastodonApiTest {

    @Test
    fun testIsRegistrationOpenReturnsCorrectStatus() = runTest {
        val mockClient = mockk<HttpClient>()
        val domain = "example.com"
        val expected = MastodonInstanceResponse(hasRegistrationOpen = true)
        coEvery {
            mockClient.get("https://$domain${MastodonConstant.INSTANCE_ENDPOINT}")
                .body<MastodonInstanceResponse>()
        } returns expected

        val api = MastodonApi(mockClient)
        val result = api.isRegistrationOpen(domain)
        assertTrue(result)
        coVerify {
            mockClient.get("https://$domain${MastodonConstant.INSTANCE_ENDPOINT}")
        }
    }

    @Test
    fun testRegisterAppReturnsClientCredentials() = runTest {
        val appRegistrationResponse = AppRegistrationResponse(
            clientId = "test-client-id",
            clientSecret = "test-client-secret"
        )
        val mockClient = mockk<HttpClient>()
        val domain = "testdomain.com"
        val mockResponse = mockk<io.ktor.client.statement.HttpResponse>()
        coEvery {
            mockClient.post("https://$domain${MastodonConstant.APPS_ENDPOINT}", any<Any>())
        } returns mockResponse
        coEvery { mockResponse.body<AppRegistrationResponse>() } returns appRegistrationResponse

        val api = MastodonApi(mockClient)
        api.registerApp(domain).test {
            val item = awaitItem()
            assertIs<ApiResult.Success<AppRegistrationResponse>>(item)
            val data = (item as ApiResult.Success).data
            assertEquals("test-client-id", data.clientId)
            assertEquals("test-client-secret", data.clientSecret)
            awaitComplete()
        }
    }

    @Test
    fun testCheckUsernameAvailabilityReturnsCorrectResult() = runTest {
        val domain = "test.com"
        val username = "availableuser"
        val mockClient = mockk<HttpClient>()
        // Simulate 404 for available username
        val clientRequestException = ClientRequestException(
            response = mockk {
                every { status } returns HttpStatusCode.NotFound
                every { status.description } returns "Not Found"
                every { status.value } returns 404
            }
        )
        coEvery {
            mockClient.get("https://$domain${MastodonConstant.ACCOUNT_LOOKUP_ENDPOINT}?acct=${username.encodeURLParameter()}", any<Any>())
        } throws clientRequestException

        val api = MastodonApi(mockClient)
        api.checkUsernameAvailability(domain, username).test {
            val item = awaitItem()
            assertIs<ApiResult.Success<Boolean>>(item)
            assertTrue((item as ApiResult.Success).data)
            awaitComplete()
        }

        // Simulate 200 for taken username
        val takenUsername = "takenuser"
        val mockResponse = mockk<io.ktor.client.statement.HttpResponse>()
        coEvery {
            mockClient.get("https://$domain${MastodonConstant.ACCOUNT_LOOKUP_ENDPOINT}?acct=${takenUsername.encodeURLParameter()}", any<Any>())
        } returns mockResponse

        api.checkUsernameAvailability(domain, takenUsername).test {
            val item = awaitItem()
            assertIs<ApiResult.Success<Boolean>>(item)
            assertFalse((item as ApiResult.Success).data)
            awaitComplete()
        }
    }

    @Test
    fun testGetInstanceRulesHandlesTimeout() = runTest {
        val domain = "timeout.com"
        val mockClient = mockk<HttpClient>()
        val timeoutException = io.ktor.client.plugins.HttpRequestTimeoutException(mockk())
        coEvery {
            mockClient.get("https://$domain${MastodonConstant.INSTANCE_RULES_ENDPOINT}", any<Any>())
        } throws timeoutException

        val api = MastodonApi(mockClient)
        api.getInstanceRules(domain).test {
            val item = awaitItem()
            assertIs<ApiResult.Error>(item)
            assertContains((item as ApiResult.Error).message, "Network error")
            awaitComplete()
        }
    }

    @Test
    fun testExchangeCodeForTokenHandlesInvalidCode() = runTest {
        val domain = "invalid.com"
        val clientId = "cid"
        val clientSecret = "csecret"
        val code = "badcode"
        val redirectUri = "urn:ietf:wg:oauth:2.0:oob"
        val mockClient = mockk<HttpClient>()
        val clientRequestException = ClientRequestException(
            response = mockk {
                every { status } returns HttpStatusCode.BadRequest
                every { status.description } returns "Bad Request"
            }
        )
        coEvery {
            mockClient.post("https://$domain${MastodonConstant.OAUTH_TOKEN_ENDPOINT}", any<Any>())
        } throws clientRequestException

        val api = MastodonApi(mockClient)
        api.exchangeCodeForToken(domain, clientId, clientSecret, code, redirectUri).test {
            val item = awaitItem()
            assertIs<ApiResult.Error>(item)
            assertContains((item as ApiResult.Error).message, "Client error")
            awaitComplete()
        }
    }

    @Test
    fun testHandlesSerializationExceptionInApiResponses() = runTest {
        val domain = "serialization.com"
        val mockClient = mockk<HttpClient>()
        val serializationException = SerializationException("Invalid JSON")
        coEvery {
            mockClient.get("https://$domain${MastodonConstant.INSTANCE_RULES_ENDPOINT}", any<Any>())
        } throws serializationException

        val api = MastodonApi(mockClient)
        api.getInstanceRules(domain).test {
            val item = awaitItem()
            assertIs<ApiResult.Error>(item)
            assertContains((item as ApiResult.Error).message, "Data parsing error")
            awaitComplete()
        }
    }

    @Test
    fun testVerifyCredentialsReturnsAccountResponse() = runTest {
        val domain = "valid.com"
        val accessToken = "validAccessToken"
        val mockClient = mockk<HttpClient>()
        val expectedAccount = AccountResponse(
            id = "123",
            username = "testuser",
            acct = "<EMAIL>",
            displayName = "Test User",
            locked = false,
            bot = false,
            createdAt = "2024-01-01T00:00:00Z",
            note = "",
            url = "https://valid.com/@testuser",
            uri = "valid.com:123",
            avatar = "https://valid.com/avatar.png",
            avatarStatic = "https://valid.com/avatar.png",
            header = "https://valid.com/header.png",
            headerStatic = "https://valid.com/header.png",
            followersCount = 10,
            followingCount = 5,
            statusesCount = 100,
            emojis = emptyList(),
            roles = emptyList(),
            fields = emptyList()
        )
        val mockResponse = mockk<io.ktor.client.statement.HttpResponse>()
        coEvery {
            mockClient.get("https://$domain${MastodonConstant.VERIFY_CREDENTIALS_ENDPOINT}", any<Any>())
        } returns mockResponse
        coEvery { mockResponse.body<AccountResponse>() } returns expectedAccount

        val api = MastodonApi(mockClient)
        api.verifyCredentials(domain, accessToken).test {
            val item = awaitItem()
            assertIs<ApiResult.Success<AccountResponse>>(item)
            val data = (item as ApiResult.Success).data
            assertEquals(expectedAccount, data)
            awaitComplete()
        }
    }

    @Test
    fun testCreateAccountHandlesServerError() = runTest {
        val mockClient = mockk<HttpClient>()
        val domain = "servererror.com"
        val appToken = "appToken"
        val userName = "user"
        val emailAddress = "<EMAIL>"
        val password = "password"
        val isAgreed = true
        val localeString = "en"

        val serverResponseException = ServerResponseException(
            response = mockk {
                // Simulate 500 Internal Server Error
                coEvery { status } returns HttpStatusCode.InternalServerError
                coEvery { status.description } returns "Internal Server Error"
            }
        )

        coEvery {
            mockClient.post("https://$domain${MastodonConstant.ACCOUNTS_ENDPOINT}", any<Any>())
        } throws serverResponseException

        val api = MastodonApi(mockClient)
        api.createAccount(
            domain = domain,
            appToken = appToken,
            userName = userName,
            emailAddress = emailAddress,
            password = password,
            isAgreed = isAgreed,
            localeString = localeString
        ).test {
            val item = awaitItem()
            assertIs<ApiResult.Error>(item)
            assertContains((item as ApiResult.Error).message, "Server error")
            awaitComplete()
        }
    }

    @Test
    fun testExchangeCodeForTokenReturnsOAuthTokenResponse() = runTest {
        val domain = "oauth.com"
        val clientId = "clientId"
        val clientSecret = "clientSecret"
        val code = "validCode"
        val redirectUri = "urn:ietf:wg:oauth:2.0:oob"
        val mockClient = mockk<HttpClient>()
        val mockResponse = mockk<io.ktor.client.statement.HttpResponse>()
        val expectedToken = OAuthTokenResponse(
            accessToken = "access_token_123",
            tokenType = "Bearer",
            scope = "read write",
            createdAt = 1710000000L
        )
        coEvery {
            mockClient.post("https://$domain${MastodonConstant.OAUTH_TOKEN_ENDPOINT}", any<Any>())
        } returns mockResponse
        coEvery { mockResponse.body<OAuthTokenResponse>() } returns expectedToken

        val api = MastodonApi(mockClient)
        api.exchangeCodeForToken(domain, clientId, clientSecret, code, redirectUri).test {
            val item = awaitItem()
            assertIs<ApiResult.Success<OAuthTokenResponse>>(item)
            val data = (item as ApiResult.Success).data
            assertEquals(expectedToken, data)
            awaitComplete()
        }
    }

    @Test
    fun testCreateAccountHandlesInvalidAppToken() = runTest {
        val mockClient = mockk<HttpClient>()
        val domain = "invalidtoken.com"
        val appToken = "invalidAppToken"
        val userName = "newuser"
        val emailAddress = "<EMAIL>"
        val password = "password"
        val isAgreed = true
        val localeString = "en"
        val clientRequestException = ClientRequestException(
            response = mockk {
                coEvery { status } returns HttpStatusCode.Unauthorized
                coEvery { status.description } returns "Unauthorized"
            }
        )
        coEvery {
            mockClient.post("https://$domain${MastodonConstant.ACCOUNTS_ENDPOINT}", any<Any>())
        } throws clientRequestException

        val api = MastodonApi(mockClient)
        api.createAccount(
            domain = domain,
            appToken = appToken,
            userName = userName,
            emailAddress = emailAddress,
            password = password,
            isAgreed = isAgreed,
            localeString = localeString
        ).test {
            val item = awaitItem()
            assertIs<ApiResult.Error>(item)
            assertContains((item as ApiResult.Error).message, "Client error")
            awaitComplete()
        }
    }
}
