package com.amoretech.memory.mastodon.api

import app.cash.turbine.test
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.mastodon.constant.MastodonConstant
import com.amoretech.memory.mastodon.dto.AccountResponse
import com.amoretech.memory.mastodon.dto.AppRegistrationResponse
import com.amoretech.memory.mastodon.dto.InstanceRuleResponse
import com.amoretech.memory.mastodon.dto.MastodonInstanceResponse
import com.amoretech.memory.mastodon.dto.OAuthTokenResponse
import com.amoretech.memory.mastodon.dto.UserTokenResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.test.runTest
import kotlinx.io.IOException
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

open class MastodonApiTest :
    BehaviorSpec({
        val testInstanceResponse = MastodonInstanceResponse(hasRegistrationOpen = true)
        val testAppRegistrationResponse = AppRegistrationResponse(
            clientId = "test-client-id",
            clientSecret = "test-client-secret"
        )
        val testOAuthTokenResponse = OAuthTokenResponse(
            accessToken = "access_token_123",
            tokenType = "Bearer",
            scope = "read write",
            createdAt = 1710000000L
        )
        val testAccountResponse = AccountResponse(
            id = "123",
            username = "testuser",
            acct = "<EMAIL>",
            displayName = "Test User",
            locked = false,
            bot = false,
            createdAt = "2024-01-01T00:00:00Z",
            note = "",
            url = "https://example.com/@testuser",
            uri = "example.com:123",
            avatar = "https://example.com/avatar.png",
            avatarStatic = "https://example.com/avatar.png",
            header = "https://example.com/header.png",
            headerStatic = "https://example.com/header.png",
            followersCount = 10,
            followingCount = 5,
            statusesCount = 100,
            emojis = emptyList(),
            roles = emptyList(),
            fields = emptyList()
        )

        fun createApiWithEngine(engine: MockEngine) =
            MastodonApi(
                httpClient = HttpClient(engine) {
                    install(ContentNegotiation) {
                        json(
                            Json {
                                ignoreUnknownKeys = true
                                encodeDefaults = true
                            }
                        )
                    }
                }
            )

        given("MastodonApi") {
            `when`("checking registration status with valid response") {
                val engine = MockEngine {
                    respond(
                        content = ByteReadChannel(Json.encodeToString(testInstanceResponse)),
                        status = HttpStatusCode.OK,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should return correct registration status") {
                    runTest {
                        val result = api.isRegistrationOpen("example.com")
                        result shouldBe true
                    }
                }
            }

            `when`("registering app with valid response") {
                val engine = MockEngine {
                    respond(
                        content = ByteReadChannel(Json.encodeToString(testAppRegistrationResponse)),
                        status = HttpStatusCode.OK,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit success with app credentials") {
                    runTest {
                        api.registerApp("testdomain.com").test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Success<AppRegistrationResponse>>()
                            val success = result as ApiResult.Success<AppRegistrationResponse>
                            success.data.clientId shouldBe "test-client-id"
                            success.data.clientSecret shouldBe "test-client-secret"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("checking username availability with 404 response") {
                val engine = MockEngine {
                    respond(
                        content = "",
                        status = HttpStatusCode.NotFound,
                        headers = headersOf()
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should return username is available") {
                    runTest {
                        api.checkUsernameAvailability("test.com", "availableuser").test {
                            val result = awaitItem()

                            // Debug: Print what we actually got
                            println("DEBUG: Actual result type: ${result::class}")
                            println("DEBUG: Actual result: $result")

                            result.shouldBeInstanceOf<ApiResult.Success<Boolean>>()
                            val success = result as ApiResult.Success<Boolean>
                            success.data shouldBe true

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("checking username availability with 200 response") {
                val engine = MockEngine {
                    respond(
                        content = ByteReadChannel("{}"),
                        status = HttpStatusCode.OK,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should return username is taken") {
                    runTest {
                        api.checkUsernameAvailability("test.com", "takenuser").test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Success<Boolean>>()
                            val success = result as ApiResult.Success<Boolean>
                            success.data shouldBe false

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("getting instance rules with network timeout") {
                val engine = MockEngine {
                    throw IOException("Request timeout")
                }
                val api = createApiWithEngine(engine)

                then("it should emit network error") {
                    runTest {
                        api.getInstanceRules("timeout.com").test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Network error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("exchanging code for token with invalid code") {
                val engine = MockEngine {
                    respond(
                        content = "",
                        status = HttpStatusCode.BadRequest,
                        headers = headersOf()
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit client error") {
                    runTest {
                        api.exchangeCodeForToken(
                            domain = "invalid.com",
                            clientId = "cid",
                            clientSecret = "csecret",
                            code = "badcode",
                            redirectUri = "urn:ietf:wg:oauth:2.0:oob"
                        ).test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Client error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("exchanging code for token with valid code") {
                val engine = MockEngine {
                    respond(
                        content = ByteReadChannel(Json.encodeToString(testOAuthTokenResponse)),
                        status = HttpStatusCode.OK,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit success with OAuth token") {
                    runTest {
                        api.exchangeCodeForToken(
                            domain = "oauth.com",
                            clientId = "clientId",
                            clientSecret = "clientSecret",
                            code = "validCode",
                            redirectUri = "urn:ietf:wg:oauth:2.0:oob"
                        ).test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Success<OAuthTokenResponse>>()
                            val success = result as ApiResult.Success<OAuthTokenResponse>
                            success.data shouldBe testOAuthTokenResponse

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("getting instance rules with serialization error") {
                val engine = MockEngine {
                    respond(
                        content = ByteReadChannel("invalid json"),
                        status = HttpStatusCode.OK,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit data parsing error") {
                    runTest {
                        api.getInstanceRules("serialization.com").test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Data parsing error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("verifying credentials with valid token") {
                val engine = MockEngine {
                    respond(
                        content = ByteReadChannel(Json.encodeToString(testAccountResponse)),
                        status = HttpStatusCode.OK,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit success with account response") {
                    runTest {
                        api.verifyCredentials("valid.com", "validAccessToken").test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Success<AccountResponse>>()
                            val success = result as ApiResult.Success<AccountResponse>
                            success.data shouldBe testAccountResponse

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("creating account with server error") {
                val engine = MockEngine {
                    respond(
                        content = "",
                        status = HttpStatusCode.InternalServerError,
                        headers = headersOf()
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit server error") {
                    runTest {
                        api.createAccount(
                            domain = "servererror.com",
                            appToken = "appToken",
                            userName = "user",
                            emailAddress = "<EMAIL>",
                            password = "password",
                            isAgreed = true,
                            localeString = "en"
                        ).test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Server error"

                            awaitComplete()
                        }
                    }
                }
            }

            `when`("creating account with invalid app token") {
                val engine = MockEngine {
                    respond(
                        content = "",
                        status = HttpStatusCode.Unauthorized,
                        headers = headersOf()
                    )
                }
                val api = createApiWithEngine(engine)

                then("it should emit client error") {
                    runTest {
                        api.createAccount(
                            domain = "invalidtoken.com",
                            appToken = "invalidAppToken",
                            userName = "newuser",
                            emailAddress = "<EMAIL>",
                            password = "password",
                            isAgreed = true,
                            localeString = "en"
                        ).test {
                            val result = awaitItem()

                            result.shouldBeInstanceOf<ApiResult.Error>()
                            val error = result as ApiResult.Error
                            error.message shouldContain "Client error"

                            awaitComplete()
                        }
                    }
                }
            }
        }
    })
