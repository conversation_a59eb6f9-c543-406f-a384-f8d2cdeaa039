package com.amoretech.memory.mastodon.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SourceDto(
    @SerialName("privacy")
    val privacy: String? = null,
    @SerialName("sensitive")
    val sensitive: Boolean? = null,
    @SerialName("language")
    val language: String? = null,
    @SerialName("note")
    val note: String? = null,
    @SerialName("fields")
    val fields: List<FieldDto> = emptyList(),
    @SerialName("follow_requests_count")
    val followRequestsCount: Int? = null,
    @SerialName("hide_collections")
    val hideCollections: Boolean? = null,
    @SerialName("discoverable")
    val discoverable: Boolean? = null,
    @SerialName("indexable")
    val indexable: Boolean? = null,
    @SerialName("attribution_domains")
    val attributionDomains: List<String> = emptyList(),
)
