package com.amoretech.memory.mastodon.constant

object MastodonConstant {
    const val INSTANCE_ENDPOINT = "/api/v1/instance"
    const val INSTANCE_RULES_ENDPOINT = "/api/v1/instance/rules"
    const val APPS_ENDPOINT = "/api/v1/apps"
    const val OAUTH_AUTHORIZE_ENDPOINT = "/oauth/authorize"
    const val OAUTH_TOKEN_ENDPOINT = "/oauth/token"
    const val ACCOUNTS_ENDPOINT = "/api/v1/accounts"
    const val RESEND_CONFIRMATION_ENDPOINT = "/api/v1/emails/confirmations"
    const val VERIFY_CREDENTIALS_ENDPOINT = "/api/v1/accounts/verify_credentials"
    const val ACCOUNT_LOOKUP_ENDPOINT = "/api/v1/accounts/lookup"

    // const val DEFAULT_REDIRECT_URI = "urn:ietf:wg:oauth:2.0:oob"
    const val DEFAULT_REDIRECT_URI = "memory-client://oauth/callback"
    const val DEFAULT_SCOPES = "read write follow push"
    const val DEFAULT_CLIENT_NAME = "Memory"
    const val TIME_OUT_MILISECONDS = 5000L
}
