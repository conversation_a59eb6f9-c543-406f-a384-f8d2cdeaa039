package com.amoretech.memory.mastodon.api

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.logging.MemoryLogger
import com.amoretech.memory.mastodon.constant.MastodonConstant
import com.amoretech.memory.mastodon.dto.AccountResponse
import com.amoretech.memory.mastodon.dto.AppRegistrationResponse
import com.amoretech.memory.mastodon.dto.InstanceRuleResponse
import com.amoretech.memory.mastodon.dto.MastodonInstanceResponse
import com.amoretech.memory.mastodon.dto.OAuthTokenResponse
import com.amoretech.memory.mastodon.dto.TokenResponse
import com.amoretech.memory.mastodon.dto.UserTokenResponse
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.RedirectResponseException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.plugins.timeout
import io.ktor.client.request.forms.FormDataContent
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.HttpHeaders
import io.ktor.http.Parameters
import io.ktor.http.encodeURLParameter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.io.IOException
import kotlinx.serialization.SerializationException

class MastodonApi(
    private val httpClient: HttpClient,
) {
    suspend fun isRegistrationOpen(domain: String): Boolean =
        try {
            val response: MastodonInstanceResponse = httpClient.get("https://$domain${MastodonConstant.INSTANCE_ENDPOINT}").body()
            MemoryLogger.i { "isRegistrationOpen response success: $response" }
            response.hasRegistrationOpen
        } catch (e: Exception) {
            MemoryLogger.i { "isRegistrationOpen response catch error: $e" }
            false
        }

    fun getInstanceRules(domain: String): Flow<ApiResult<List<InstanceRuleResponse>>> =
        flow<ApiResult<List<InstanceRuleResponse>>> {
            val response =
                httpClient.get("https://$domain${MastodonConstant.INSTANCE_RULES_ENDPOINT}") {
                    timeout { requestTimeoutMillis = MastodonConstant.TIME_OUT_MILISECONDS }
                }
            MemoryLogger.i { "getInstanceRules response success: $response" }
            emit(ApiResult.Success(response.body()))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "getInstanceRules response catch error: $e" }
                emit(handleException(e))
            }

    fun registerApp(domain: String): Flow<ApiResult<AppRegistrationResponse>> =
        flow<ApiResult<AppRegistrationResponse>> {
            val response =
                httpClient.post("https://$domain${MastodonConstant.APPS_ENDPOINT}") {
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append("client_name", MastodonConstant.DEFAULT_CLIENT_NAME)
                                append("redirect_uris", MastodonConstant.DEFAULT_REDIRECT_URI)
                                append("scopes", MastodonConstant.DEFAULT_SCOPES)
                            },
                        ),
                    )
                }
            MemoryLogger.i { "registerApp response success: $response" }
            emit(ApiResult.Success(response.body()))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "registerApp response catch error: $e" }
                emit(handleException(e))
            }

    fun buildAuthorizationUrl(
        domain: String,
        clientId: String,
        redirectUri: String = MastodonConstant.DEFAULT_REDIRECT_URI,
        scopes: String = MastodonConstant.DEFAULT_SCOPES,
    ): String =
        "https://$domain${MastodonConstant.OAUTH_AUTHORIZE_ENDPOINT}?" +
            "client_id=${clientId.encodeURLParameter()}" +
            "&redirect_uri=${redirectUri.encodeURLParameter()}" +
            "&response_type=code" +
            "&scope=${scopes.encodeURLParameter()}"

    fun exchangeCodeForToken(
        domain: String,
        clientId: String,
        clientSecret: String,
        code: String,
        redirectUri: String = "urn:ietf:wg:oauth:2.0:oob",
    ): Flow<ApiResult<OAuthTokenResponse>> =
        flow<ApiResult<OAuthTokenResponse>> {
            val response =
                httpClient.post("https://$domain${MastodonConstant.OAUTH_TOKEN_ENDPOINT}") {
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append("client_id", clientId)
                                append("client_secret", clientSecret)
                                append("redirect_uri", redirectUri)
                                append("grant_type", "authorization_code")
                                append("code", code)
                            },
                        ),
                    )
                }
            MemoryLogger.i { "exchangeCodeForToken response success: $response" }
            emit(ApiResult.Success(response.body()))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "exchangeCodeForToken response catch error: $e" }
                emit(handleException(e))
            }

    suspend fun getAppToken(
        domain: String,
        clientId: String,
        clientSecret: String,
    ): Flow<ApiResult<TokenResponse>> =
        flow<ApiResult<TokenResponse>> {
            val response =
                httpClient.post("https://$domain${MastodonConstant.OAUTH_TOKEN_ENDPOINT}") {
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append("client_id", clientId)
                                append("client_secret", clientSecret)
                                append("grant_type", "client_credentials")
                                append("scope", "write:accounts")
                            },
                        ),
                    )
                }
            MemoryLogger.i { "getAppToken response success: $response" }
            emit(ApiResult.Success(response.body()))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "getAppToken response catch error: $e" }
                emit(handleException(e))
            }

    suspend fun createAccount(
        domain: String,
        appToken: String,
        userName: String,
        emailAddress: String,
        password: String,
        isAgreed: Boolean,
        localeString: String,
    ): Flow<ApiResult<UserTokenResponse>> =
        flow<ApiResult<UserTokenResponse>> {
            val response =
                httpClient.post("https://$domain${MastodonConstant.ACCOUNTS_ENDPOINT}") {
                    header(HttpHeaders.Authorization, "Bearer $appToken")
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append("username", userName)
                                append("email", emailAddress)
                                append("password", password)
                                append("agreement", isAgreed.toString())
                                append("locale", localeString)
                            },
                        ),
                    )
                }
            MemoryLogger.i { "createAccount response success: $response" }
            emit(ApiResult.Success(response.body()))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "createAccount response catch error: $e" }
                emit(handleException(e))
            }

    suspend fun resendConfirmation(
        domain: String,
        userToken: String,
    ): Flow<ApiResult<Boolean>> =
        flow<ApiResult<Boolean>> {
            httpClient.post("https://$domain${MastodonConstant.RESEND_CONFIRMATION_ENDPOINT}") {
                header(HttpHeaders.Authorization, "Bearer $userToken")
            }
            MemoryLogger.i { "resendConfirmation response success" }
            emit(ApiResult.Success(true))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "resendConfirmation response catch error: $e" }
                emit(handleException(e))
            }

    suspend fun verifyCredentials(
        domain: String,
        accessToken: String,
    ): Flow<ApiResult<AccountResponse>> =
        flow<ApiResult<AccountResponse>> {
            val response =
                httpClient.get("https://$domain${MastodonConstant.VERIFY_CREDENTIALS_ENDPOINT}") {
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                }
            MemoryLogger.i { "verifyCredentials response success: $response" }
            emit(ApiResult.Success(response.body()))
        }.flowOn(Dispatchers.IO)
            .catch { e ->
                MemoryLogger.i { "verifyCredentials response catch error: $e" }
                emit(handleException(e))
            }

    suspend fun checkUsernameAvailability(
        domain: String,
        username: String,
    ): Flow<ApiResult<Boolean>> =
        flow {
            try {
                httpClient.get("https://$domain${MastodonConstant.ACCOUNT_LOOKUP_ENDPOINT}?acct=${username.encodeURLParameter()}") {
                    timeout { requestTimeoutMillis = MastodonConstant.TIME_OUT_MILISECONDS }
                }
                MemoryLogger.i { "checkUsernameAvailability response success" }
                // to check if we get a response, that means username is taken
                emit(ApiResult.Success(false))
            } catch (e: ClientRequestException) {
                if (e.response.status.value == 404) {
                    // user name is available
                    emit(ApiResult.Success(true))
                } else {
                    // user name is not available
                    emit(ApiResult.Error("Username check failed: ${e.response.status.description}", e))
                }
            } catch (e: Exception) {
                MemoryLogger.i { "checkUsernameAvailability response catch error: $e" }
                emit(handleException(e))
            }
        }.flowOn(Dispatchers.IO)

    private fun handleException(e: Throwable): ApiResult.Error =
        when (e) {
            is RedirectResponseException ->
                ApiResult.Error("Redirection error: ${e.response.status.description}", e)

            is ClientRequestException ->
                ApiResult.Error("Client error: ${e.response.status.description}", e)

            is ServerResponseException ->
                ApiResult.Error("Server error: ${e.response.status.description}", e)

            is SerializationException ->
                ApiResult.Error("Data parsing error: ${e.message ?: "Invalid response format"}", e)

            is IOException ->
                ApiResult.Error("Network error: ${e.message ?: "Connection failed"}", e)

            else ->
                ApiResult.Error("Unexpected error: ${e.message ?: "Unknown failure"}")
        }
}
