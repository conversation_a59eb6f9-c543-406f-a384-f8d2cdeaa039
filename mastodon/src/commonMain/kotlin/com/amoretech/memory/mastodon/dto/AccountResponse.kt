package com.amoretech.memory.mastodon.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AccountResponse(
    @SerialName("id")
    val id: String,
    @SerialName("username")
    val username: String,
    @SerialName("acct")
    val acct: String,
    @SerialName("display_name")
    val displayName: String,
    @SerialName("locked")
    val locked: Boolean,
    @SerialName("bot")
    val bot: Boolean,
    @SerialName("discoverable")
    val discoverable: Boolean? = null,
    @SerialName("indexable")
    val indexable: Boolean? = null,
    @SerialName("group")
    val group: Boolean? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("note")
    val note: String,
    @SerialName("url")
    val url: String,
    @SerialName("uri")
    val uri: String,
    @SerialName("avatar")
    val avatar: String,
    @SerialName("avatar_static")
    val avatarStatic: String,
    @SerialName("header")
    val header: String,
    @SerialName("header_static")
    val headerStatic: String,
    @SerialName("followers_count")
    val followersCount: Int,
    @SerialName("following_count")
    val followingCount: Int,
    @SerialName("statuses_count")
    val statusesCount: Int,
    @SerialName("last_status_at")
    val lastStatusAt: String? = null,
    @SerialName("hide_collections")
    val hideCollections: Boolean? = null,
    @SerialName("noindex")
    val noindex: Boolean? = null,
    @SerialName("source")
    val source: SourceDto? = null,
    @SerialName("emojis")
    val emojis: List<EmojiDto> = emptyList(),
    @SerialName("roles")
    val roles: List<RoleDto> = emptyList(),
    @SerialName("fields")
    val fields: List<FieldDto> = emptyList(),
    @SerialName("role")
    val role: RoleDto? = null,
)
