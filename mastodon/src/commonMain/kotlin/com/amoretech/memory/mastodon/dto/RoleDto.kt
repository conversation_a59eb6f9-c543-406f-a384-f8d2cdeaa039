package com.amoretech.memory.mastodon.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RoleDto(
    @SerialName("id")
    val id: String,
    @SerialName("name")
    val name: String,
    @SerialName("permissions")
    val permissions: String,
    @SerialName("color")
    val color: String,
    @SerialName("highlighted")
    val highlighted: <PERSON><PERSON><PERSON>,
)
