plugins {
    // this is necessary to avoid the plugins to be loaded multiple times
    // in each subproject's classloader
    alias(libs.plugins.androidApplication) apply false
    alias(libs.plugins.androidLibrary) apply false
    alias(libs.plugins.jetbrainsCompose) apply false
    alias(libs.plugins.composeCompiler) apply false
    alias(libs.plugins.kotlinMultiplatform) apply false
    alias(libs.plugins.androidKotlinMultiplatformLibrary) apply false
    alias(libs.plugins.kotlin.serialization) apply false
    alias(libs.plugins.kotlin.parcelize) version "2.1.20" apply false
    alias(libs.plugins.javafx) apply  false
}

/**
 * Registers a task that depends on all detekt tasks of the subprojects.
 *
 * This allows to run detekt on all subprojects by running the `detektAll` task.
 *
 * See also: [org.gradle.api.Task.dependsOn]
 */
tasks.register("detektAll") {
    group = "verification"
    description = "Runs detekt on all subprojects"
    dependsOn(subprojects.mapNotNull { it.tasks.findByName("detekt") })
}

/**
 * Registers a task that depends on all ktlint format tasks of the subprojects.
 *
 * This allows running ktlint format on all subprojects by executing the `ktlintFormatAll` task.
 */
tasks.register("ktlintFormatAll") {
    group = "formatting"
    description = "Runs ktlint format on all subprojects"
    dependsOn(subprojects.mapNotNull { it.tasks.findByName("ktlintFormat") })
}

/**
 * To suppress the expect/actual warning in all subprojects.
 */
subprojects {
    plugins.withId("org.jetbrains.kotlin.multiplatform") {
        extensions.configure(org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension::class.java) {
            targets.configureEach {
                compilations.configureEach {
                    compileTaskProvider.get().compilerOptions {
                        freeCompilerArgs.add("-Xexpect-actual-classes")
                    }
                }
            }
        }
    }
}