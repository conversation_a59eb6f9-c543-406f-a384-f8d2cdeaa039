# -------------------------
# Versions grouped by usage
# -------------------------

[versions]
# Android SDK & Build Tools
agp = "8.9.0"
android-compileSdk = "35"
android-minSdk = "24"
android-targetSdk = "35"

# Kotlin & Kotlin Multiplatform
junitJupiterApi = "5.10.2"
junitJupiterEngine = "5.10.2"
kotestFrameworkEngine = "5.8.1"
kotestAssertionsCore = "5.8.1"
kotestProperty = "5.8.1"
kotlin = "2.1.20"
kotlinx-coroutines = "1.10.2"
kotlinxDatetime = "0.6.2"
kotlinx-collections-immutable-v = "0.3.5"
kotlinx-serialization = "1.7.3"
kotlinx-serialization-json = "1.7.3"
atomic-fu = "0.22.0"

# AndroidX Libraries
androidx-core-ktx = "1.16.0"
androidx-appcompat = "1.7.0"
androidx-constraintlayout = "2.2.1"
androidx-activityCompose = "1.10.1"
androidx-lifecycle = "2.8.4"
materialIconsExtended = "1.7.8"
androidx-compose-foundation = "1.8.0"
androidx-browser = "1.8.0"

# Compose & UI
compose-plugin = "1.8.0"
coilCompose = "3.1.0"
compose-desktop = "1.8.0"

# Room (Database)
room = "2.7.1"
ksp = "2.1.20-2.0.1"
sqlite = "2.5.1"

# Ktor (Networking)
io-ktor = "3.1.3"

# Dependency Injection (Koin)
io-insert-koin = "4.0.3"

# Testing
androidx-espresso-core = "3.6.1"
androidx-test-junit = "1.2.1"
runner = "1.6.2"
core = "1.6.1"
truth = "1.4.4"
turbine = "1.2.1"
junit = "4.13.2"
org-jetbrains-kotlinx-coroutines = "1.10.2"

# Navigation & ViewModel (Multiplatform)
androidx-navigation-compose = "2.8.0"
androidx-lifecycle-viewmodel = "2.8.0"
androidx-lifecycle-viewmodel-compose = "2.8.0"
koin-compose-viewmodel = "4.0.0"
navigation = "2.8.0-alpha10"

# JavaFX (Desktop)
javafxPlugin = "0.1.0"
javafxLib = "19"

# Utilities
urlencoderLib = "1.6.0"

# Logging
io-github-aakira = "2.7.1"

# Store
store = "5.1.0-alpha06"

# Russhwolf (Multiplatform Settings)
com-russhwolf = "1.3.0"

# -------------------------
# Libraries grouped by usage
# -------------------------

[libraries]

# AndroidX Libraries
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidx-core-ktx" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "androidx-appcompat" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "androidx-constraintlayout" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "androidx-activityCompose" }
androidx-lifecycle-runtime-compose = { group = "org.jetbrains.androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidx-lifecycle" }
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended", version.ref = "materialIconsExtended" }
androidx-compose-foundation = { module = "androidx.compose.foundation:foundation", version.ref = "androidx-compose-foundation" }
androidx-browser = { module = "androidx.browser:browser", version.ref = "androidx-browser" }

# Compose & Coil
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coilCompose" }
coil-network-ktor = { module = "io.coil-kt.coil3:coil-network-ktor3", version.ref = "coilCompose" }
compose-desktop = { module = "org.jetbrains.compose.desktop:desktop", version.ref = "compose-desktop" }

# Kotlin & Coroutines
kotlin-parcelize-runtime = { module = "org.jetbrains.kotlin:kotlin-parcelize-runtime", version.ref = "kotlin" }
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib", version.ref = "kotlin" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinx-collections-immutable = { module = "org.jetbrains.kotlinx:kotlinx-collections-immutable", version.ref = "kotlinx-collections-immutable-v" }
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "kotlinx-coroutines" }
kotlinx-coroutines-javafx = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-javafx", version.ref = "kotlinx-coroutines" }
kotlinx-coroutines-swing = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-swing", version.ref = "kotlinx-coroutines" }
kotlinx-serialization-core = { module = "org.jetbrains.kotlinx:kotlinx-serialization-core", version.ref = "kotlinx-serialization" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinx-serialization-json" }
org-jetbrains-kotlinx-atomicfu = { module = "org.jetbrains.kotlinx:atomicfu", version.ref = "atomic-fu" }

# Room (Database)
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-runtime-android = { group = "androidx.room", name = "room-runtime-android", version.ref = "room" }
sqlite-bundled = { module = "androidx.sqlite:sqlite-bundled", version.ref = "sqlite" }

# Ktor (Networking)
io-ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "io-ktor" }
io-ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "io-ktor" }
io-ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "io-ktor" }
io-ktor-client-darwin = { module = "io.ktor:ktor-client-darwin", version.ref = "io-ktor" }
io-ktor-client-android = { module = "io.ktor:ktor-client-android", version.ref = "io-ktor" }
io-ktor-client-java = { group = "io.ktor", name = "ktor-client-java", version.ref = "io-ktor" }
io-ktor-client-serialization = { module = "io.ktor:ktor-client-serialization", version.ref = "io-ktor" }
io-ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "io-ktor" }
io-ktor-client-plugins-timeout = { module = "io.ktor:ktor-client-plugins-timeout", version.ref = "io-ktor" }
io-ktor-client-auth = { module = "io.ktor:ktor-client-auth", version.ref = "io-ktor" }
io-ktor-client-mock-jvm = { module = "io.ktor:ktor-client-mock-jvm", version.ref = "io-ktor" }
io-ktor-client-mock = { module = "io.ktor:ktor-client-mock", version.ref = "io-ktor" }
io-ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "io-ktor" }
io-ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "io-ktor" }
io-ktor-client-url-parsing = { module = "io.ktor:ktor-http", version.ref = "io-ktor" }

# Dependency Injection (Koin)
io-insert-koin-core = { module = "io.insert-koin:koin-core", version.ref = "io-insert-koin" }
io-insert-koin-test = { module = "io.insert-koin:koin-test", version.ref = "io-insert-koin" }
io-insert-koin-android = { module = "io.insert-koin:koin-android", version.ref = "io-insert-koin" }
io-insert-koin-compose = { module = "io.insert-koin:koin-compose", version.ref = "io-insert-koin" }

# Navigation & ViewModel (Multiplatform)
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "androidx-navigation-compose" }
androidx-lifecycle-viewmodel = { module = "androidx.lifecycle:lifecycle-viewmodel", version.ref = "androidx-lifecycle-viewmodel" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "androidx-lifecycle-viewmodel-compose" }
koin-compose-viewmodel = { module = "io.insert-koin:koin-compose-viewmodel", version.ref = "koin-compose-viewmodel" }
compose-navigation = { module = "org.jetbrains.androidx.navigation:navigation-compose", version.ref = "navigation" }

# Testing
androidx-test-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidx-test-junit" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "androidx-espresso-core" }
androidx-runner = { group = "androidx.test", name = "runner", version.ref = "runner" }
androidx-core = { group = "androidx.test", name = "core", version.ref = "core" }
kotlin-test = { module = "org.jetbrains.kotlin:kotlin-test", version.ref = "kotlin" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
org-jetbrains-kotlin-test-junit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlin" }
org-jetbrains-kotlin-test-common = { module = "org.jetbrains.kotlin:kotlin-test-common", version.ref = "kotlin" }
org-jetbrains-kotlin-test-annotations-common = { module = "org.jetbrains.kotlin:kotlin-test-annotations-common", version.ref = "kotlin" }
turbine = { module = "app.cash.turbine:turbine", version.ref = "turbine" }
org-jetbrains-kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "org-jetbrains-kotlinx-coroutines" }
kotest-property = { module = "io.kotest:kotest-property", version.ref = "kotestProperty" }
kotest-assertions-core = { module = "io.kotest:kotest-assertions-core", version.ref = "kotestAssertionsCore" }
kotest-framework-engine = { module = "io.kotest:kotest-framework-engine", version.ref = "kotestFrameworkEngine" }
truth = { module = "com.google.truth:truth", version.ref = "truth" }
junit-jupiter-engine = { module = "org.junit.jupiter:junit-jupiter-engine", version.ref = "junitJupiterEngine" }
junit-jupiter-api = { module = "org.junit.jupiter:junit-jupiter-api", version.ref = "junitJupiterApi" }

# Logging
io-github-aakira-napier = { module = "io.github.aakira:napier", version.ref = "io-github-aakira" }

# Utilities
store = { module = "org.mobilenativefoundation.store:store5", version.ref = "store" }
multiplatform-settings = { module = "com.russhwolf:multiplatform-settings", version.ref = "com-russhwolf" }
multiplatform-settings-coroutines = { module = "com.russhwolf:multiplatform-settings-coroutines", version.ref = "com-russhwolf" }
urlencoder-lib = { module = "net.thauvin.erik.urlencoder:urlencoder-lib", version.ref = "urlencoderLib" }

# JavaFX (Desktop)
javafxLib = { module = "org.openjfx:javafx-controls", version.ref = "javafxLib" }

# -------------------------
# Plugins
# -------------------------

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
jetbrainsCompose = { id = "org.jetbrains.compose", version.ref = "compose-plugin" }
composeCompiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlinMultiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
androidKotlinMultiplatformLibrary = { id = "com.android.kotlin.multiplatform.library", version.ref = "agp" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
javafx = { id = "org.openjfx.javafxplugin", version.ref = "javafxPlugin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
room = { id = "androidx.room", version.ref = "room" }
