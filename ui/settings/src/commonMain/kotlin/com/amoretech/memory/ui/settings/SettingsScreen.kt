package com.amoretech.memory.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import org.koin.compose.koinInject

@Suppress("ktlint:standard:function-naming")
/**
 * Settings screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier,
    onNavigateBack: () -> Unit,
    onNavigateToLanding: () -> Unit,
    viewModel: SettingsViewModel = koinInject(),
) {
    // Collect the UI state from the ViewModel
    val state by viewModel.uiState.collectAsState()

    // Handle effects
    LaunchedEffect(Unit) {
        // Collect the effects from the ViewModel
        viewModel.effect.collect { effect ->
            // Handle the effect
            when (effect) {
                is SettingsEffect.NavigateToLanding -> {
                    // Navigate to the landing screen
                    onNavigateToLanding()
                }
            }
        }
    }

    // Create a Scaffold with a top app bar
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Settings") },
                navigationIcon = {
                    // Add a back button
                    IconButton(onClick = onNavigateBack) {
                        //  Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
            )
        },
        modifier = modifier,
    ) { paddingValues ->
        // Create a column to hold the settings
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
        ) {
            // Create a card to hold the account settings
            Card(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                ) {
                    // Add a title to the card
                    Text(
                        text = "Account",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp),
                    )

                    // Add a logout button
                    TextButton(
                        onClick = {
                            // Handle the logout intent
                            viewModel.handleIntent(SettingsIntent.Logout)
                        },
                        enabled = !state.isLoggingOut,
                    ) {
                        // Display the logout text
                        Text(
                            text = if (state.isLoggingOut) "Logging out..." else "Logout",
                            color = MaterialTheme.colorScheme.error,
                        )
                    }
                }
            }

            // Display any errors
            state.error?.let { error ->
                Text(
                    text = "Error: $error",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 16.dp),
                )
            }
        }
    }
}
