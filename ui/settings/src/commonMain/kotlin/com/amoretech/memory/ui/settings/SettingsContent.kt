package com.amoretech.memory.ui.settings

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.amoretech.memory.ui.common.composables.button.MemoryButton
import com.amoretech.memory.ui.common.composables.button.buttonColors

@Composable
fun SettingsContent(
    component: SettingsComponent,
    modifier: Modifier = Modifier,
) {
    SettingsContent(
        modifier = modifier,
        onLogout = component::logout,
    )
}

@Composable
internal fun SettingsContent(
    onLogout: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.BottomCenter,
    ) {
        MemoryButton(
            text = "Logout",
            onClick = onLogout,
            colors = buttonColors(backgroundColor = MaterialTheme.colorScheme.error),
        )
    }
}
