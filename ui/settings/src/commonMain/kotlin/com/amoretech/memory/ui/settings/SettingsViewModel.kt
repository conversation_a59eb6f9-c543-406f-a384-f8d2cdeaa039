package com.amoretech.memory.ui.settings

import com.amoretech.memory.domain.authentication.usecase.LogoutFromCurrentServer
import com.amoretech.memory.ui.common.mvi.MviEffect
import com.amoretech.memory.ui.common.mvi.MviIntent
import com.amoretech.memory.ui.common.mvi.MviState
import com.amoretech.memory.ui.common.mvi.MviViewModel
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Settings ViewModel with MVI pattern
 */
class SettingsViewModel :
    MviViewModel<SettingsIntent, SettingsState, SettingsEffect>(
        initialState = SettingsState(),
    ),
    KoinComponent {
    private val logoutFromCurrentServer: LogoutFromCurrentServer by inject()

    override suspend fun handleIntentInternal(intent: SettingsIntent) {
        when (intent) {
            is SettingsIntent.Logout -> {
                logout()
            }
        }
    }

    private suspend fun logout() {
        updateState { copy(isLoggingOut = true) }

        try {
            logoutFromCurrentServer()
            sendEffect(SettingsEffect.NavigateToLanding)
        } catch (e: Exception) {
            updateState {
                copy(
                    isLoggingOut = false,
                    error = "Failed to logout: ${e.message}",
                )
            }
        }
    }
}

/**
 * Settings Intents (User Actions)
 */
sealed interface SettingsIntent : MviIntent {
    object Logout : SettingsIntent
}

/**
 * Settings State (UI State)
 */
data class SettingsState(
    val isLoggingOut: Boolean = false,
    val error: String? = null,
) : MviState

/**
 * Settings Effects (One-time events)
 */
sealed interface SettingsEffect : MviEffect {
    object NavigateToLanding : SettingsEffect
}
