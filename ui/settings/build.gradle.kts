import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.composeCompiler)
    id("detekt")
    id("ktlint")
}

kotlin {

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.ui.settings"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "settingsKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(libs.kotlin.stdlib)
                implementation(projects.ui.common)
                implementation(projects.domain.authentication)
                implementation(projects.data.persistence)
                implementation(projects.data.repository)
                implementation(libs.io.insert.koin.core)
                implementation(libs.io.insert.koin.compose)
                implementation(compose.runtime)
                implementation(compose.foundation)
                implementation(compose.material3)
                implementation(compose.ui)
                implementation(compose.materialIconsExtended)
                implementation(libs.koin.compose.viewmodel)
                // implementation(libs.com.arkivanov.decompose.essenty.lifecycle.coroutines)
                implementation(libs.coil.compose)
                implementation(libs.coil.network.ktor)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.androidx.core.ktx)
                implementation(libs.androidx.compose.foundation)
                implementation(libs.io.ktor.client.android)
                implementation(libs.androidx.material.icons.extended)
                implementation(libs.androidx.lifecycle.viewmodel)
                implementation(libs.androidx.lifecycle.viewmodel.compose)
            }
        }

        iosMain {
            dependencies {
                implementation(libs.io.ktor.client.darwin)
            }
        }

        jvmMain {
            dependencies {
                implementation(libs.compose.desktop)
                implementation(libs.io.ktor.client.java)
                implementation(compose.materialIconsExtended)
            }
        }

        jvmTest {
            dependencies {}
        }
    }
}
