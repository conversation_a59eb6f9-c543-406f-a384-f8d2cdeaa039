package com.amoretech.memory.ui.root.root.effect

import com.amoretech.memory.ui.common.mvi.MviEffect

/**
 * Root Effects (One-time events)
 */
sealed interface RootEffect : MviEffect {
    data object NavigateToTimeline : RootEffect

    data object NavigateToLanding : RootEffect

    data class NavigateToOobVerification(
        val domain: String,
        val email: String,
    ) : RootEffect

    data class NavigateToSignIn(
        val domain: String,
    ) : RootEffect
}
