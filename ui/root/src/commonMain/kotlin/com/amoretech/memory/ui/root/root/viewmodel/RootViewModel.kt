package com.amoretech.memory.ui.root.root.viewmodel

import com.amoretech.memory.data.persistence.setting.AppSetting
import com.amoretech.memory.data.persistence.setting.UserState
import com.amoretech.memory.domain.authentication.model.AuthStatus
import com.amoretech.memory.domain.authentication.usecase.GetAuthStatus
import com.amoretech.memory.ui.common.mvi.MviViewModel
import com.amoretech.memory.ui.root.root.effect.RootEffect
import com.amoretech.memory.ui.root.root.event.RootIntent
import com.amoretech.memory.ui.root.root.state.RootState
import com.amoretech.memory.ui.root.splash.state.UiAuthStatus
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Root ViewModel that manages authentication state and navigation
 */
class RootViewModel :
    MviViewModel<RootIntent, RootState, RootEffect>(
        initialState = RootState(),
    ),
    KoinComponent {
    private val getAuthStatus: GetAuthStatus by inject()
    private val appSetting: AppSetting by inject()

    init {
        observeUserState()
    }

    override suspend fun handleIntentInternal(intent: RootIntent) {
        when (intent) {
            is RootIntent.CheckAuthStatus -> {
                // User state is automatically observed
            }
        }
    }

    /**
     * Observes both authentication status and user state to determine navigation
     */
    private fun observeUserState() {
        combine(
            getAuthStatus(),
            appSetting.userStateFlow
        ) { authStatus, userState ->
            Pair(authStatus, userState)
        }.onEach { (authStatus, userState) ->
            updateState {
                copy(
                    authStatus = when (authStatus) {
                        is AuthStatus.Authorized -> UiAuthStatus.Authorized
                        is AuthStatus.Unauthorized -> UiAuthStatus.Unauthorized
                    },
                    isLoading = false,
                )
            }

            // Enhanced navigation logic based on user state
            determineNavigation(authStatus, userState)
        }.launchIn(viewModelScope)
    }

    /**
     * Determines navigation destination based on authentication status and user state
     */
    private suspend fun determineNavigation(authStatus: AuthStatus, userState: UserState) {
        when {
            // User is fully authenticated - go to timeline
            authStatus is AuthStatus.Authorized && userState == UserState.AUTHENTICATED -> {
                sendEffect(RootEffect.NavigateToTimeline)
            }

            // User is registered but needs OOB verification
            userState == UserState.REGISTERED_PENDING_OOB -> {
                val email = appSetting.userEmail
                val domain = appSetting.serverName
                if (email != null && domain != null) {
                    sendEffect(RootEffect.NavigateToOobVerification(domain, email))
                } else {
                    // Fallback to landing if data is missing
                    sendEffect(RootEffect.NavigateToLanding)
                }
            }

            // User is verified but needs to complete OAuth sign-in
            userState == UserState.VERIFIED_PENDING_AUTH -> {
                val domain = appSetting.serverName
                if (domain != null) {
                    sendEffect(RootEffect.NavigateToSignIn(domain))
                } else {
                    sendEffect(RootEffect.NavigateToLanding)
                }
            }

            // Default case - user is unregistered or unauthorized
            else -> {
                sendEffect(RootEffect.NavigateToLanding)
            }
        }
    }
}
