package com.amoretech.memory.ui.root.root.viewmodel

import com.amoretech.memory.domain.authentication.model.AuthStatus
import com.amoretech.memory.domain.authentication.usecase.GetAuthStatus
import com.amoretech.memory.ui.common.mvi.MviViewModel
import com.amoretech.memory.ui.root.root.effect.RootEffect
import com.amoretech.memory.ui.root.root.event.RootIntent
import com.amoretech.memory.ui.root.root.state.RootState
import com.amoretech.memory.ui.root.splash.state.UiAuthStatus
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Root ViewModel that manages authentication state and navigation
 */
class RootViewModel :
    MviViewModel<RootIntent, RootState, RootEffect>(
        initialState = RootState(),
    ),
    KoinComponent {
    private val getAuthStatus: GetAuthStatus by inject()

    init {
        observeAuthStatus()
    }

    override suspend fun handleIntentInternal(intent: RootIntent) {
        when (intent) {
            is RootIntent.CheckAuthStatus -> {
                // Auth status is automatically observed
            }
        }
    }

    private fun observeAuthStatus() {
        getAuthStatus()
            .onEach { authStatus ->
                updateState {
                    copy(
                        authStatus =
                            when (authStatus) {
                                is AuthStatus.Authorized -> UiAuthStatus.Authorized
                                is AuthStatus.Unauthorized -> UiAuthStatus.Unauthorized
                            },
                        isLoading = false,
                    )
                }

                // Send navigation effect based on auth status
                when (authStatus) {
                    is AuthStatus.Authorized -> {
                        sendEffect(RootEffect.NavigateToTimeline)
                    }
                    is AuthStatus.Unauthorized -> {
                        sendEffect(RootEffect.NavigateToLanding)
                    }
                }
            }.launchIn(viewModelScope)
    }
}
