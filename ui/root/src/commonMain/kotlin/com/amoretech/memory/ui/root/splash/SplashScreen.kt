package com.amoretech.memory.ui.root.splash

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Splash screen that shows while checking authentication status
 */
@Composable
@Suppress("ktlint:standard:function-naming")
fun SplashScreen(
    modifier: Modifier = Modifier,
    navigateToLanding: () -> Unit,
    navigateToSignIn: () -> Unit,
//    isLoading: Boolean,
//    authStatus: UiAuthStatus,
) {
    val isLoading = false

    // todo change it to auth state later
    LaunchedEffect(Unit) {
        if (!isLoading) {
            navigateToLanding()
        } else {
            navigateToSignIn()
        }
    }

    Column(
        modifier =
            modifier
                .fillMaxSize()
                .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = "Memory",
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.primary,
        )

        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.padding(top = 32.dp),
            )

            Text(
                text = "Loading...",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(top = 16.dp),
            )
        }
    }
}
