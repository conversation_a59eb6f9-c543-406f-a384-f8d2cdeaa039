package com.amoretech.memory.ui.root.splash

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.amoretech.memory.ui.root.root.effect.RootEffect
import com.amoretech.memory.ui.root.root.event.RootIntent
import com.amoretech.memory.ui.root.root.viewmodel.RootViewModel
import org.koin.compose.koinInject

/**
 * Splash screen that shows while checking authentication status
 */
@Composable
@Suppress("ktlint:standard:function-naming")
fun SplashScreen(
    modifier: Modifier = Modifier,
    navigateToLanding: () -> Unit,
    navigateToTimeline: () -> Unit,
    navigateToOobVerification: (domain: String, email: String) -> Unit,
    navigateToSignIn: (domain: String) -> Unit,
    viewModel: RootViewModel = koinInject(),
) {
    val state by viewModel.uiState.collectAsState()

    // Initialize auth status check
    LaunchedEffect(Unit) {
        viewModel.handleIntent(RootIntent.CheckAuthStatus)
    }

    // Handle navigation effects
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is RootEffect.NavigateToLanding -> {
                    navigateToLanding()
                }
                is RootEffect.NavigateToTimeline -> {
                    navigateToTimeline()
                }
                is RootEffect.NavigateToOobVerification -> {
                    navigateToOobVerification(effect.domain, effect.email)
                }
                is RootEffect.NavigateToSignIn -> {
                    navigateToSignIn(effect.domain)
                }
            }
        }
    }

    Column(
        modifier =
            modifier
                .fillMaxSize()
                .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = "Memory",
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.primary,
        )

        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.padding(top = 32.dp),
            )

            Text(
                text = "Loading...",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(top = 16.dp),
            )
        }
    }
}
