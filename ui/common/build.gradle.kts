import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.composeCompiler)
    alias(libs.plugins.kotlin.serialization)
    id("detekt")
    id("ktlint")
}

kotlin {

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.ui.common"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "commonKit"

    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64(),
    ).forEach { iosTarget ->
        iosTarget.binaries.framework {
            baseName = xcfName
            isStatic = true
            export(project(":ui:common"))
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(compose.runtime)
                implementation(compose.foundation)
                implementation(compose.material3)
                implementation(compose.ui)
                implementation(compose.materialIconsExtended)
                implementation(compose.components.resources)
                implementation(compose.materialIconsExtended)
                implementation(libs.kotlin.stdlib)
                implementation(projects.data.network)
                implementation(projects.logging)
                implementation(libs.kotlinx.serialization.json)
                implementation(libs.coil.compose)
                implementation(libs.coil.network.ktor)
                implementation(libs.io.insert.koin.core)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.androidx.core.ktx)
                implementation(libs.androidx.compose.foundation)
                implementation(libs.io.ktor.client.android)
                implementation(libs.androidx.material.icons.extended)
            }
        }

        iosMain {
            dependencies {
                implementation(libs.io.ktor.client.darwin)
                resources.srcDirs("src/iosMain/resources")
            }
        }

        jvmMain {
            dependencies {
                implementation(libs.compose.desktop)
                implementation(libs.io.ktor.client.java)
                implementation(compose.materialIconsExtended)
            }
        }

        jvmTest {
            dependencies {}
        }
    }
}

// because the dependency on the compose library is a project dependency
compose.resources {
    publicResClass = true
    generateResClass = always
    packageOfResClass = "memory.ui.common.generated.resources"
}

tasks.withType<Copy>().configureEach {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
