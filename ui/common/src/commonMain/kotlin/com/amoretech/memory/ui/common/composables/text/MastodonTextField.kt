package com.amoretech.memory.ui.common.composables.text

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.ZeroCornerSize
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.amoretech.memory.ui.common.theme.MemoryTheme

@Suppress("ktlint:standard:function-naming")
@Composable
fun MemoryTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    enabled: Boolean = true,
    isError: Boolean = false,
    singleLine: Boolean = false,
    maxLines: Int = Int.MAX_VALUE,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    colors: TextFieldColors = textFieldColors(),
) {
    TextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        enabled = enabled,
        readOnly = false,
        textStyle = LocalTextStyle.current,
        label = label,
        placeholder = placeholder,
        leadingIcon = leadingIcon,
        trailingIcon = trailingIcon,
        isError = isError,
        singleLine = singleLine,
        maxLines = maxLines,
        shape = MaterialTheme.shapes.small.copy(bottomEnd = ZeroCornerSize, bottomStart = ZeroCornerSize),
        colors = colors,
    )
}

/**
 * Creates a [TextFieldColors] that represents the default input text, background and content
 * (including label, placeholder, leading and trailing icons) colors used in a [TextField].
 */
@Composable
private fun textFieldColors(
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    disabledTextColor: Color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f),
    backgroundColor: Color = Color.Transparent,
    cursorColor: Color = MaterialTheme.colorScheme.primary,
    errorCursorColor: Color = MaterialTheme.colorScheme.error,
    focusedIndicatorColor: Color = MaterialTheme.colorScheme.primary,
    unfocusedIndicatorColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    disabledIndicatorColor: Color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
    errorIndicatorColor: Color = MaterialTheme.colorScheme.error,
    leadingIconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    disabledLeadingIconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
    errorLeadingIconColor: Color = MaterialTheme.colorScheme.error,
    trailingIconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    disabledTrailingIconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
    errorTrailingIconColor: Color = MaterialTheme.colorScheme.error,
    focusedLabelColor: Color = MaterialTheme.colorScheme.primary,
    unfocusedLabelColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    disabledLabelColor: Color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
    errorLabelColor: Color = MaterialTheme.colorScheme.error,
    placeholderColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    disabledPlaceholderColor: Color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
): TextFieldColors =
    TextFieldDefaults.colors(
        focusedTextColor = textColor,
        disabledTextColor = disabledTextColor,
        focusedContainerColor = backgroundColor,
        unfocusedContainerColor = backgroundColor,
        disabledContainerColor = backgroundColor,
        cursorColor = cursorColor,
        errorCursorColor = errorCursorColor,
        focusedIndicatorColor = focusedIndicatorColor,
        unfocusedIndicatorColor = unfocusedIndicatorColor,
        disabledIndicatorColor = disabledIndicatorColor,
        errorIndicatorColor = errorIndicatorColor,
        focusedLeadingIconColor = leadingIconColor,
        unfocusedLeadingIconColor = leadingIconColor,
        disabledLeadingIconColor = disabledLeadingIconColor,
        errorLeadingIconColor = errorLeadingIconColor,
        focusedTrailingIconColor = trailingIconColor,
        unfocusedTrailingIconColor = trailingIconColor,
        disabledTrailingIconColor = disabledTrailingIconColor,
        errorTrailingIconColor = errorTrailingIconColor,
        focusedLabelColor = focusedLabelColor,
        unfocusedLabelColor = unfocusedLabelColor,
        disabledLabelColor = disabledLabelColor,
        errorLabelColor = errorLabelColor,
        focusedPlaceholderColor = placeholderColor,
        unfocusedPlaceholderColor = placeholderColor,
        disabledPlaceholderColor = disabledPlaceholderColor,
    )

// @Immutable
// private class DefaultTextFieldColors(
//    private val textColor: Color,
//    private val disabledTextColor: Color,
//    private val cursorColor: Color,
//    private val errorCursorColor: Color,
//    private val focusedIndicatorColor: Color,
//    private val unfocusedIndicatorColor: Color,
//    private val errorIndicatorColor: Color,
//    private val disabledIndicatorColor: Color,
//    private val leadingIconColor: Color,
//    private val disabledLeadingIconColor: Color,
//    private val errorLeadingIconColor: Color,
//    private val trailingIconColor: Color,
//    private val disabledTrailingIconColor: Color,
//    private val errorTrailingIconColor: Color,
//    private val backgroundColor: Color,
//    private val focusedLabelColor: Color,
//    private val unfocusedLabelColor: Color,
//    private val disabledLabelColor: Color,
//    private val errorLabelColor: Color,
//    private val placeholderColor: Color,
//    private val disabledPlaceholderColor: Color
// ) : TextFieldColors {
//
//    @Composable
//    override fun leadingIconColor(enabled: Boolean, isError: Boolean): State<Color> {
//        return rememberUpdatedState(
//            when {
//                !enabled -> disabledLeadingIconColor
//                isError -> errorLeadingIconColor
//                else -> leadingIconColor
//            }
//        )
//    }
//
//    @Composable
//    override fun trailingIconColor(enabled: Boolean, isError: Boolean): State<Color> {
//        return rememberUpdatedState(
//            when {
//                !enabled -> disabledTrailingIconColor
//                isError -> errorTrailingIconColor
//                else -> trailingIconColor
//            }
//        )
//    }
//
//    @Composable
//    override fun indicatorColor(
//        enabled: Boolean,
//        isError: Boolean,
//        interactionSource: InteractionSource
//    ): State<Color> {
//        val focused by interactionSource.collectIsFocusedAsState()
//
//        val targetValue = when {
//            !enabled -> disabledIndicatorColor
//            isError -> errorIndicatorColor
//            focused -> focusedIndicatorColor
//            else -> unfocusedIndicatorColor
//        }
//        return if (enabled) {
//            animateColorAsState(targetValue, tween(durationMillis = AnimationDuration))
//        } else {
//            rememberUpdatedState(targetValue)
//        }
//    }
//
//    @Composable
//    override fun backgroundColor(enabled: Boolean): State<Color> {
//        return rememberUpdatedState(backgroundColor)
//    }
//
//    @Composable
//    override fun placeholderColor(enabled: Boolean): State<Color> {
//        return rememberUpdatedState(if (enabled) placeholderColor else disabledPlaceholderColor)
//    }
//
//    @Composable
//    override fun labelColor(
//        enabled: Boolean,
//        error: Boolean,
//        interactionSource: InteractionSource
//    ): State<Color> {
//        val focused by interactionSource.collectIsFocusedAsState()
//
//        val targetValue = when {
//            !enabled -> disabledLabelColor
//            error -> errorLabelColor
//            focused -> focusedLabelColor
//            else -> unfocusedLabelColor
//        }
//        return rememberUpdatedState(targetValue)
//    }
//
//    @Composable
//    override fun textColor(enabled: Boolean): State<Color> {
//        return rememberUpdatedState(if (enabled) textColor else disabledTextColor)
//    }
//
//    @Composable
//    override fun cursorColor(isError: Boolean): State<Color> {
//        return rememberUpdatedState(if (isError) errorCursorColor else cursorColor)
//    }
//
//    override fun equals(other: Any?): Boolean {
//        if (this === other) return true
//        if (other == null || this::class != other::class) return false
//
//        other as DefaultTextFieldColors
//
//        if (textColor != other.textColor) return false
//        if (disabledTextColor != other.disabledTextColor) return false
//        if (cursorColor != other.cursorColor) return false
//        if (errorCursorColor != other.errorCursorColor) return false
//        if (focusedIndicatorColor != other.focusedIndicatorColor) return false
//        if (unfocusedIndicatorColor != other.unfocusedIndicatorColor) return false
//        if (errorIndicatorColor != other.errorIndicatorColor) return false
//        if (disabledIndicatorColor != other.disabledIndicatorColor) return false
//        if (leadingIconColor != other.leadingIconColor) return false
//        if (disabledLeadingIconColor != other.disabledLeadingIconColor) return false
//        if (errorLeadingIconColor != other.errorLeadingIconColor) return false
//        if (trailingIconColor != other.trailingIconColor) return false
//        if (disabledTrailingIconColor != other.disabledTrailingIconColor) return false
//        if (errorTrailingIconColor != other.errorTrailingIconColor) return false
//        if (backgroundColor != other.backgroundColor) return false
//        if (focusedLabelColor != other.focusedLabelColor) return false
//        if (unfocusedLabelColor != other.unfocusedLabelColor) return false
//        if (disabledLabelColor != other.disabledLabelColor) return false
//        if (errorLabelColor != other.errorLabelColor) return false
//        if (placeholderColor != other.placeholderColor) return false
//        if (disabledPlaceholderColor != other.disabledPlaceholderColor) return false
//
//        return true
//    }
//
//    override fun hashCode(): Int {
//        var result = textColor.hashCode()
//        result = 31 * result + disabledTextColor.hashCode()
//        result = 31 * result + cursorColor.hashCode()
//        result = 31 * result + errorCursorColor.hashCode()
//        result = 31 * result + focusedIndicatorColor.hashCode()
//        result = 31 * result + unfocusedIndicatorColor.hashCode()
//        result = 31 * result + errorIndicatorColor.hashCode()
//        result = 31 * result + disabledIndicatorColor.hashCode()
//        result = 31 * result + leadingIconColor.hashCode()
//        result = 31 * result + disabledLeadingIconColor.hashCode()
//        result = 31 * result + errorLeadingIconColor.hashCode()
//        result = 31 * result + trailingIconColor.hashCode()
//        result = 31 * result + disabledTrailingIconColor.hashCode()
//        result = 31 * result + errorTrailingIconColor.hashCode()
//        result = 31 * result + backgroundColor.hashCode()
//        result = 31 * result + focusedLabelColor.hashCode()
//        result = 31 * result + unfocusedLabelColor.hashCode()
//        result = 31 * result + disabledLabelColor.hashCode()
//        result = 31 * result + errorLabelColor.hashCode()
//        result = 31 * result + placeholderColor.hashCode()
//        result = 31 * result + disabledPlaceholderColor.hashCode()
//        return result
//    }
// }

// @Composable
// fun MemoryPageTitle(
//    modifier: Modifier = Modifier,
//    text:String?
// ) {
//    if (text != null) {
//        Text(
//            text = text,
//            modifier = modifier,
//            style = TextStyle(
//                fontSize = 28.sp,
//                fontFamily = FontFamily(Font(R.font.))
//            )
//
//        )
//    }
//    else{
//        Text(
//            text = "Memory",
//            modifier = modifier
//        )
//    }
// }

private const val AnimationDuration = 150

// @androidx.compose.desktop.ui.tooling.preview.Preview
@Composable
fun PreviewTextField(modifier: Modifier = Modifier) {
    MemoryTheme {
        Surface {
            var text by remember { mutableStateOf("Hello World") }
            MemoryTextField(
                value = text,
                onValueChange = { text = it },
                modifier =
                    modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp),
            )
        }
    }
}
