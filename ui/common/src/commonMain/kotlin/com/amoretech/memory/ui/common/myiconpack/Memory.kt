package com.amoretech.memory.ui.common.myiconpack

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val MyIconPack.Memory: ImageVector
    get() {
        if (_memory != null) {
            return _memory!!
        }
        _memory =
            Builder(
                name = "Memory",
                defaultWidth = 106.0.dp,
                defaultHeight = 21.0.dp,
                viewportWidth = 106.0f,
                viewportHeight = 21.0f,
            ).apply {
                path(
                    fill = SolidColor(Color(0xFF373737)),
                    stroke = null,
                    strokeLineWidth = 0.0f,
                    strokeLineCap = Butt,
                    strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f,
                    pathFillType = NonZero,
                ) {
                    moveTo(21.663f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(15.895f)
                    verticalLineTo(13.524f)
                    curveTo(16.959f, 13.216f, 17.015f, 12.012f, 17.015f, 10.584f)
                    verticalLineTo(5.152f)
                    curveTo(17.015f, 3.584f, 16.231f, 2.828f, 14.971f, 2.828f)
                    curveTo(13.935f, 2.828f, 12.759f, 4.088f, 12.759f, 5.208f)
                    verticalLineTo(10.584f)
                    curveTo(12.759f, 12.124f, 12.899f, 13.272f, 13.907f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(8.139f)
                    verticalLineTo(13.524f)
                    curveTo(9.203f, 13.216f, 9.259f, 12.012f, 9.259f, 10.584f)
                    verticalLineTo(5.152f)
                    curveTo(9.259f, 3.584f, 8.503f, 2.828f, 7.243f, 2.828f)
                    curveTo(6.207f, 2.828f, 5.003f, 4.116f, 5.003f, 5.124f)
                    verticalLineTo(10.64f)
                    curveTo(5.003f, 12.18f, 5.171f, 13.244f, 6.151f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(0.411f)
                    verticalLineTo(13.524f)
                    curveTo(1.475f, 13.216f, 1.503f, 12.068f, 1.503f, 10.64f)
                    verticalLineTo(4.508f)
                    curveTo(1.503f, 3.528f, 1.251f, 3.024f, 0.103f, 2.408f)
                    verticalLineTo(1.932f)
                    lineTo(5.003f, 0.812f)
                    verticalLineTo(3.696f)
                    curveTo(5.759f, 2.072f, 7.467f, 0.812f, 9.231f, 0.812f)
                    curveTo(11.191f, 0.812f, 12.395f, 1.708f, 12.731f, 3.78f)
                    curveTo(13.319f, 2.072f, 15.223f, 0.812f, 16.987f, 0.812f)
                    curveTo(19.171f, 0.812f, 20.515f, 1.68f, 20.515f, 5.068f)
                    verticalLineTo(10.584f)
                    curveTo(20.515f, 12.124f, 20.655f, 13.272f, 21.663f, 13.524f)
                    close()
                    moveTo(26.5f, 6.804f)
                    horizontalLineTo(31.989f)
                    curveTo(31.989f, 3.276f, 30.84f, 1.288f, 29.301f, 1.288f)
                    curveTo(27.649f, 1.288f, 26.5f, 4.116f, 26.5f, 6.804f)
                    close()
                    moveTo(29.441f, 0.812f)
                    curveTo(33.5f, 0.812f, 35.404f, 2.912f, 35.404f, 7.28f)
                    horizontalLineTo(26.5f)
                    curveTo(26.725f, 10.024f, 28.125f, 11.452f, 30.0f, 11.872f)
                    curveTo(31.513f, 12.208f, 33.752f, 11.648f, 34.452f, 10.332f)
                    lineTo(34.929f, 10.584f)
                    curveTo(34.48f, 12.04f, 32.828f, 14.252f, 29.076f, 14.252f)
                    curveTo(25.409f, 14.252f, 22.889f, 10.864f, 22.889f, 7.588f)
                    curveTo(22.889f, 3.64f, 25.576f, 0.812f, 29.441f, 0.812f)
                    close()
                    moveTo(58.204f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(52.436f)
                    verticalLineTo(13.524f)
                    curveTo(53.5f, 13.216f, 53.556f, 12.012f, 53.556f, 10.584f)
                    verticalLineTo(5.152f)
                    curveTo(53.556f, 3.584f, 52.772f, 2.828f, 51.512f, 2.828f)
                    curveTo(50.476f, 2.828f, 49.3f, 4.088f, 49.3f, 5.208f)
                    verticalLineTo(10.584f)
                    curveTo(49.3f, 12.124f, 49.44f, 13.272f, 50.448f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(44.68f)
                    verticalLineTo(13.524f)
                    curveTo(45.744f, 13.216f, 45.8f, 12.012f, 45.8f, 10.584f)
                    verticalLineTo(5.152f)
                    curveTo(45.8f, 3.584f, 45.044f, 2.828f, 43.784f, 2.828f)
                    curveTo(42.748f, 2.828f, 41.544f, 4.116f, 41.544f, 5.124f)
                    verticalLineTo(10.64f)
                    curveTo(41.544f, 12.18f, 41.712f, 13.244f, 42.692f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(36.952f)
                    verticalLineTo(13.524f)
                    curveTo(38.016f, 13.216f, 38.044f, 12.068f, 38.044f, 10.64f)
                    verticalLineTo(4.508f)
                    curveTo(38.044f, 3.528f, 37.792f, 3.024f, 36.644f, 2.408f)
                    verticalLineTo(1.932f)
                    lineTo(41.544f, 0.812f)
                    verticalLineTo(3.696f)
                    curveTo(42.3f, 2.072f, 44.008f, 0.812f, 45.772f, 0.812f)
                    curveTo(47.732f, 0.812f, 48.936f, 1.708f, 49.272f, 3.78f)
                    curveTo(49.86f, 2.072f, 51.764f, 0.812f, 53.528f, 0.812f)
                    curveTo(55.712f, 0.812f, 57.056f, 1.68f, 57.056f, 5.068f)
                    verticalLineTo(10.584f)
                    curveTo(57.056f, 12.124f, 57.196f, 13.272f, 58.204f, 13.524f)
                    close()
                    moveTo(66.234f, 0.812f)
                    curveTo(70.518f, 0.812f, 72.898f, 3.836f, 72.898f, 7.532f)
                    curveTo(72.898f, 11.2f, 70.518f, 14.252f, 66.234f, 14.252f)
                    curveTo(61.978f, 14.252f, 59.57f, 11.2f, 59.57f, 7.532f)
                    curveTo(59.57f, 3.836f, 61.978f, 0.812f, 66.234f, 0.812f)
                    close()
                    moveTo(66.234f, 13.776f)
                    curveTo(68.25f, 13.776f, 68.754f, 12.6f, 68.754f, 7.532f)
                    curveTo(68.754f, 2.464f, 68.25f, 1.288f, 66.234f, 1.288f)
                    curveTo(64.218f, 1.288f, 63.686f, 2.464f, 63.686f, 7.532f)
                    curveTo(63.686f, 12.6f, 64.218f, 13.776f, 66.234f, 13.776f)
                    close()
                    moveTo(82.405f, 0.952f)
                    curveTo(82.993f, 0.7f, 84.057f, 0.812f, 84.673f, 1.316f)
                    curveTo(85.54f, 2.072f, 85.624f, 3.416f, 84.868f, 4.256f)
                    curveTo(84.113f, 5.124f, 82.825f, 5.236f, 81.928f, 4.48f)
                    curveTo(81.508f, 4.088f, 81.285f, 3.612f, 81.285f, 3.108f)
                    curveTo(81.285f, 2.8f, 80.136f, 3.08f, 79.101f, 4.76f)
                    verticalLineTo(10.584f)
                    curveTo(79.101f, 12.152f, 79.241f, 13.244f, 80.248f, 13.524f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(74.452f)
                    verticalLineTo(13.524f)
                    curveTo(75.545f, 13.188f, 75.573f, 12.04f, 75.573f, 10.584f)
                    verticalLineTo(4.368f)
                    curveTo(75.573f, 3.416f, 75.321f, 2.996f, 74.173f, 2.408f)
                    verticalLineTo(1.932f)
                    lineTo(79.101f, 0.812f)
                    verticalLineTo(3.976f)
                    curveTo(80.585f, 1.652f, 81.732f, 1.232f, 82.405f, 0.952f)
                    close()
                    moveTo(94.715f, 1.064f)
                    horizontalLineTo(100.203f)
                    verticalLineTo(1.54f)
                    curveTo(99.615f, 1.54f, 98.327f, 1.596f, 96.535f, 5.908f)
                    lineTo(92.251f, 17.192f)
                    curveTo(91.523f, 19.096f, 90.683f, 19.88f, 90.151f, 20.3f)
                    curveTo(89.647f, 20.72f, 88.667f, 20.888f, 87.995f, 20.58f)
                    curveTo(87.015f, 20.132f, 86.595f, 18.984f, 87.015f, 18.004f)
                    curveTo(87.463f, 17.024f, 88.611f, 16.576f, 89.591f, 17.024f)
                    curveTo(90.095f, 17.248f, 90.403f, 17.696f, 90.543f, 18.172f)
                    curveTo(90.655f, 18.508f, 91.327f, 18.312f, 91.775f, 17.136f)
                    lineTo(92.839f, 14.252f)
                    curveTo(91.103f, 10.248f, 87.547f, 2.632f, 87.547f, 2.632f)
                    curveTo(87.099f, 1.54f, 86.203f, 1.54f, 85.783f, 1.54f)
                    verticalLineTo(1.064f)
                    horizontalLineTo(92.391f)
                    verticalLineTo(1.54f)
                    curveTo(90.935f, 1.54f, 91.663f, 3.248f, 91.663f, 3.248f)
                    lineTo(94.631f, 9.576f)
                    lineTo(96.115f, 5.796f)
                    curveTo(96.115f, 5.796f, 97.963f, 1.512f, 94.715f, 1.54f)
                    verticalLineTo(1.064f)
                    close()
                    moveTo(105.388f, 11.788f)
                    curveTo(105.388f, 13.104f, 104.324f, 14.14f, 103.036f, 14.14f)
                    curveTo(101.72f, 14.14f, 100.656f, 13.104f, 100.656f, 11.788f)
                    curveTo(100.656f, 10.472f, 101.72f, 9.408f, 103.036f, 9.408f)
                    curveTo(104.324f, 9.408f, 105.388f, 10.472f, 105.388f, 11.788f)
                    close()
                }
            }.build()
        return _memory!!
    }

private var _memory: ImageVector? = null
