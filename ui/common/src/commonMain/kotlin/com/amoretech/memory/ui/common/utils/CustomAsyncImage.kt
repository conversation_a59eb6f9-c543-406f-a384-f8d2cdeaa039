package com.amoretech.memory.ui.common.utils

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Spacer
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.produceState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import com.amoretech.memory.data.network.util.runCatchingIgnoreCancelled
import com.amoretech.memory.logging.MemoryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.withContext

/**
 * Use this helper until we switch to a image loading library which supports multiplatform
 */
@Composable
fun <T> CustomAsyncImage(
    contentDescription: String,
    load: suspend () -> T,
    painterFor: @Composable (T) -> Painter,
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ContentScale.Fit,
) {
    val image: T? by produceState<T?>(null) {
        value =
            withContext(Dispatchers.IO) {
                runCatchingIgnoreCancelled {
                    load()
                }.fold(
                    onSuccess = { it },
                    onFailure = { t ->
                        MemoryLogger.w(throwable = t) {
                            "Error when loading image."
                        }
                        null
                    },
                )
            }
    }

    if (image != null) {
        Image(
            painter = painterFor(image!!),
            contentDescription = contentDescription,
            contentScale = contentScale,
            modifier = modifier,
            alignment = alignment,
        )
    } else {
        Spacer(
            modifier = modifier,
        )
    }
}
