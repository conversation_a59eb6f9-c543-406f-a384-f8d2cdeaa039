package com.amoretech.memory.ui.common.composables

import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.amoretech.memory.ui.common.theme.MemoryTheme
// import com.amoretech.memory.ui.common.utils.AsyncImage
// import com.amoretech.memory.ui.common.utils.loadImageIntoPainter

@Composable
fun UserAvatar(
    modifier: Modifier = Modifier,
    url: String,
) {
    AsyncImage(
        modifier =
            modifier
                .width(48.dp) // Set the desired width
                .clip(RoundedCornerShape(5.dp)),
        // Apply rounded corners
        model = url, // The URL of the image
        contentDescription = "User avatar", // Accessibility content description
        // Add other Coil parameters as needed, e.g., placeholder, error, transitions
        // placeholder = painterResource(R.drawable.placeholder),
        // error = painterResource(R.drawable.error_image),
        // transformations = listOf(CircleCropTransformation())
    )
//    AsyncImage(
//        load = { loadImageIntoPainter(url = url) },
//        painterFor = { remember { it } },
//        contentDescription = "User avatar",
//        modifier = modifier.width(48.dp).clip(RoundedCornerShape(5.dp))
//    )
}

// skip preview to work with multiplatform
// https://github.com/JetBrains/compose-jb/issues/1603
// @Preview
@Composable
private fun UserPreview() {
    MemoryTheme {
        UserAvatar(url = "https://media.mastodon.cloud/accounts/avatars/000/018/251/original/e78973b0b821c7e3.jpg")
    }
}
