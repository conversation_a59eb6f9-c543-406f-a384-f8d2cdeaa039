package com.amoretech.memory.ui.common.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

val Blue = Color(0xFF4772F5)
val LightBlue = Color(0xff8EC8F0)
val DarkGrey = Color(0xFF1E202A)
val LightGrey = Color(0xFFBABBBE)
val Green = Color(0xff69f0ae)

val CoolBlue = Color(0xFF007BFF)
val DarkCoolBlue = Color(0xFF0056B3)
val LightCoolBlue = Color(0xFF66B3FF)
val AccentTeal = Color(0xFF17A2B8)
val DarkAccentTeal = Color(0xFF138496)
val LightGreyBackground = Color(0xFFF8F9FA)
val DarkGreySurface = Color(0xFF2C3E50)
val TextDark = Color(0xFF212529)
val TextLight = Color(0xFFF8F9FA)
val ErrorRed = Color(0xFFDC3545)

//
// val darkColors =
//    darkColorScheme(
//        primary = Blue,
//        onPrimary = Color.White,
//        secondary = Green,
//        onSecondary = LightBlue,
//        surface = DarkGrey,
//        onSurface = Color.White,
//    )

// val lightColors =
//    lightColorScheme(
//        primary = Blue,
//        onPrimary = Color.White,
//        secondary = Green,
//        onSecondary = LightBlue,
//        surface = Color.White,
//        onSurface = DarkGrey,
//    )

val lightColors =
    lightColorScheme(
        primary = CoolBlue,
        onPrimary = TextLight,
        primaryContainer = LightCoolBlue,
        onPrimaryContainer = DarkCoolBlue,
        secondary = AccentTeal,
        onSecondary = TextLight,
        secondaryContainer = AccentTeal.copy(alpha = 0.2f),
        onSecondaryContainer = DarkAccentTeal,
        tertiary = LightCoolBlue,
        onTertiary = TextDark,
        tertiaryContainer = CoolBlue.copy(alpha = 0.1f),
        onTertiaryContainer = TextDark,
        background = LightGreyBackground,
        onBackground = TextDark,
        surface = TextLight,
        onSurface = TextDark,
        surfaceVariant = Color(0xFFE9ECEF),
        onSurfaceVariant = TextDark,
        error = ErrorRed,
        onError = TextLight,
        errorContainer = ErrorRed.copy(alpha = 0.1f),
        onErrorContainer = ErrorRed,
        outline = TextDark.copy(alpha = 0.3f),
        scrim = Color.Black.copy(alpha = 0.4f),
    )

val darkColors =
    darkColorScheme(
        primary = LightCoolBlue,
        onPrimary = DarkGreySurface,
        primaryContainer = CoolBlue,
        onPrimaryContainer = TextLight,
        secondary = AccentTeal,
        onSecondary = DarkGreySurface,
        secondaryContainer = AccentTeal.copy(alpha = 0.4f),
        onSecondaryContainer = TextLight,
        tertiary = CoolBlue,
        onTertiary = TextLight,
        tertiaryContainer = CoolBlue.copy(alpha = 0.5f),
        onTertiaryContainer = TextLight,
        background = Color(0xFF1A2B3C),
        onBackground = TextLight,
        surface = DarkGreySurface,
        onSurface = TextLight,
        surfaceVariant = Color(0xFF34495E),
        onSurfaceVariant = TextLight,
        error = ErrorRed,
        onError = TextLight,
        errorContainer = ErrorRed.copy(alpha = 0.3f),
        onErrorContainer = TextLight,
        outline = TextLight.copy(alpha = 0.3f),
        scrim = Color.Black.copy(alpha = 0.6f),
    )
