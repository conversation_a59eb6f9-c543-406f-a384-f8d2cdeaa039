package com.amoretech.memory.ui.common.screen

import kotlinx.serialization.Serializable

// Navigation states
sealed class AppScreen {
    @Serializable
    data object Splash : AppScreen()

    @Serializable
    data object Landing : AppScreen()

    @Serializable
    data object SelectServer : AppScreen()

    @Serializable
    data class CreateAccount(
        val domain: String,
    ) : AppScreen()

    @Serializable
    data class Rules(
        val domain: String,
    ) : AppScreen()

    @Serializable
    data class WebViewScreen(
        val url: String,
    ) : AppScreen()

    @Serializable
    data class SignIn(
        val server: String,
        val oauthUrl: String,
        val redirectUri: String,
    ) : AppScreen()

    @Serializable
    data class OobVerification(
        val domain: String,
        val email: String,
    ) : AppScreen()

    @Serializable
    data object Timeline : AppScreen()

    @Serializable
    data object Settings : AppScreen()
}
