package com.amoretech.memory.ui.common.screen

import kotlinx.serialization.Serializable

// Navigation states
sealed class AppScreen {
    @Serializable
    data object Splash : AppScreen()

    @Serializable
    data object Landing : AppScreen()

    @Serializable
    data object SelectServer : AppScreen()

    @Serializable
    data class SignIn(
        val server: String,
        val oauthUrl: String,
        val redirectUri: String,
    ) : AppScreen()

    @Serializable
    data object Timeline : AppScreen()

    @Serializable
    data object Settings : AppScreen()
}
