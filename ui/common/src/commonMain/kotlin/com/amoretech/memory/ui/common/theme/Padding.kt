package com.amoretech.memory.ui.common.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * A data class that defines various padding values for use in the application's UI.
 * These values are used to ensure consistent spacing and layout across the app.
 */
data class Padding(
    val zero: Dp = 0.dp,
    val two: Dp = 2.dp,
    val four: Dp = 4.dp,
    val eight: Dp = 8.dp,
    val ten: Dp = 10.dp,
    val twelve: Dp = 12.dp,
    val sixteen: Dp = 16.dp,
    val twenty: Dp = 20.dp,
    val twentyFour: Dp = 24.dp,
    val thirtyTwo: Dp = 32.dp,
    val forty: Dp = 40.dp,
    val fortyEight: Dp = 48.dp,
    val sixtyFour: Dp = 64.dp,
)

val LocalPadding = compositionLocalOf { Padding() }

val MaterialTheme.padding: Padding
    @Composable
    @ReadOnlyComposable
    get() = LocalPadding.current
