package com.amoretech.memory.ui.common.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider

@Suppress("ktlint:standard:function-naming")
@Composable
fun MemoryTheme(
    useDarkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {
    CompositionLocalProvider(
        LocalPadding provides Padding(),
        LocalCornerRadius provides CornerRadius(),
        LocalStrokeWidth provides StrokeWidth(),
        LocalSize provides Size(),
    ) {
        MaterialTheme(
            colorScheme =
                if (useDarkTheme) {
                    darkColors
                } else {
                    lightColors
                },
            typography = MemoryTypography,
            content = content,
            shapes = shapes,
        )
    }
}
