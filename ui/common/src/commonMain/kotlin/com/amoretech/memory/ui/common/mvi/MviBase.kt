package com.amoretech.memory.ui.common.mvi

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * Base interface for MVI Intent (User Actions)
 */
interface MviIntent

/**
 * Base interface for MVI State (UI State)
 */
interface MviState

/**
 * Base interface for MVI Effect (One-time events)
 */
interface MviEffect

/**
 * Base ViewModel for MVI pattern (Multiplatform)
 */
abstract class MviViewModel<Intent : MviIntent, State : MviState, Effect : MviEffect>(
    initialState: State,
) {
    protected val viewModelScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private val _uiState = MutableStateFlow(initialState)
    val uiState: StateFlow<State> = _uiState.asStateFlow()

    private val _effect = Channel<Effect>()
    val effect = _effect.receiveAsFlow()

    /**
     * Handle user intents
     */
    fun handleIntent(intent: Intent) {
        viewModelScope.launch {
            handleIntentInternal(intent)
        }
    }

    /**
     * Update the UI state
     */
    protected fun updateState(newState: State.() -> State) {
        _uiState.update { currentState ->
            currentState.newState()
        }
    }

    /**
     * Send a one-time effect
     */
    protected fun sendEffect(effect: Effect) {
        viewModelScope.launch {
            _effect.send(effect)
        }
    }

    /**
     * Get current state
     */
    protected val currentState: State
        get() = _uiState.value

    /**
     * Handle intent implementation - to be overridden by subclasses
     */
    protected abstract suspend fun handleIntentInternal(intent: Intent)

    /**
     * Clean up resources when ViewModel is no longer needed
     */
    open fun onCleared() {
        viewModelScope.cancel()
    }
}
