package com.amoretech.memory.ui.common.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.platform.Font

@Composable
actual fun fontResources(
    font: String,
    fontWeight: FontWeight,
    fontStyle: FontStyle,
): Font {
    val fontPath =
        when (font) {
            "butler_regular_stencil.otf" -> "/font/butler_regular_stencil.otf"
            "butler_medium_stencil.otf" -> "/font/butler_medium_stencil.otf"
            "butler_bold_stencil.otf" -> "/font/butler_bold_stencil.otf"
            else -> "/font/butler_regular_stencil.otf"
        }
    return Font(fontPath, fontWeight, fontStyle)
}
