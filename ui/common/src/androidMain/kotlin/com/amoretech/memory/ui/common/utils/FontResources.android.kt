package com.amoretech.memory.ui.common.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import memory.ui.common.generated.resources.Res
import memory.ui.common.generated.resources.butler_bold_stencil
import memory.ui.common.generated.resources.butler_medium_stencil
import memory.ui.common.generated.resources.butler_regular_stencil

@Composable
actual fun fontResources(
    font: String,
    fontWeight: FontWeight,
    fontStyle: FontStyle,
): Font {
    val fontResId =
        when (font) {
            "butler_regular_stencil.otf" -> Res.font.butler_regular_stencil
            "butler_medium_stencil.otf" -> Res.font.butler_medium_stencil
            "butler_bold_stencil.otf" -> Res.font.butler_bold_stencil
            else -> Res.font.butler_regular_stencil
        }
    return org.jetbrains.compose.resources
        .Font(fontResId, fontWeight, fontStyle)
}
