package com.amoretech.memory.ui.common.utils

// @Composable
// actual fun fontResources(
//    font: String,
//    fontWeight: FontWeight,
//    fontStyle: FontStyle
// ): Font {
//    val context = LocalContext.current
//    val name = font.substringBefore(".")
//    val fontRes = context.resources.getIdentifier(name,"moko-resources/font", context.packageName)
//    return Font(fontRes, fontWeight, style = fontStyle)
// }
