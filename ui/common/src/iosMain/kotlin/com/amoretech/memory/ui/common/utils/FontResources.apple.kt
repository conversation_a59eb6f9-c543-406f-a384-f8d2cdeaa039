package com.amoretech.memory.ui.common.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight

@Composable
actual fun fontResources(
    font: String,
    fontWeight: FontWeight,
    fontStyle: FontStyle,
): Font {
    val fontName =
        when (font) {
            "fonts/butler_regular_stencil.otf" -> "ButlerRegularStencil"
            "fonts/butler_medium_stencil.otf" -> "ButlerMediumStencil"
            "fonts/butler_bold_stencil.otf" -> "ButlerBoldStencil"
            else -> "ButlerRegularStencil"
        }
    return Font(fontName, fontWeight, fontStyle)
}
