import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.composeCompiler)
    id("detekt")
    id("ktlint")
}

kotlin {

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.ui.timeline"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "timelineKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(compose.runtime)
                implementation(compose.foundation)
                implementation(compose.material3)
                implementation(compose.ui)
                implementation(compose.materialIconsExtended)
                implementation(libs.kotlin.stdlib)
                implementation(projects.domain.timeline)
                implementation(projects.data.repository)
                implementation(projects.data.network)
                implementation(projects.domain.authentication)
                implementation(projects.ui.common)
                implementation(libs.io.insert.koin.core)
                implementation(libs.koin.compose.viewmodel)
                // implementation(libs.com.arkivanov.decompose.essenty.lifecycle.coroutines)
                implementation(libs.kotlinx.collections.immutable)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.androidx.appcompat)
                implementation(libs.androidx.material.icons.extended)
                implementation(compose.foundation)
                implementation(compose.material3)
                implementation(compose.ui)
                implementation(libs.androidx.lifecycle.viewmodel)
                implementation(libs.androidx.lifecycle.viewmodel.compose)
            }
        }

        iosMain {
            dependencies {}
        }

        jvmMain {
            dependencies {
                implementation(libs.compose.desktop)
                implementation(compose.materialIconsExtended)
            }
        }

        jvmTest {
            dependencies {}
        }
    }
}
