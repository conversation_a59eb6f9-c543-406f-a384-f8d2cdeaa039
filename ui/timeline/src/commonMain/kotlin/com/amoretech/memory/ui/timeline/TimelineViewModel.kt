package com.amoretech.memory.ui.timeline

import com.amoretech.memory.data.repository.StatusRepository
import com.amoretech.memory.data.repository.TimelineRepository
import com.amoretech.memory.data.repository.usecase.BookmarkStatus
import com.amoretech.memory.data.repository.usecase.LikeStatus
import com.amoretech.memory.data.repository.usecase.PostStatus
import com.amoretech.memory.data.repository.usecase.ReblogStatus
import com.amoretech.memory.domain.timeline.repository.FeedType
import com.amoretech.memory.domain.timeline.repository.HomeTimelineRepository
import com.amoretech.memory.ui.common.mvi.MviEffect
import com.amoretech.memory.ui.common.mvi.MviIntent
import com.amoretech.memory.ui.common.mvi.MviState
import com.amoretech.memory.ui.common.mvi.MviViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.mobilenativefoundation.store.store5.StoreReadResponse

/**
 * Timeline ViewModel with MVI pattern
 */
class TimelineViewModel :
    MviViewModel<TimelineIntent, TimelineState, TimelineEffect>(
        initialState = TimelineState(),
    ),
    KoinComponent {
    private val homeTimelineRepository: HomeTimelineRepository by inject()
    private val timelineRepository: TimelineRepository by inject()
    private val statusRepository: StatusRepository by inject()
    private val postStatus: PostStatus by inject()
    private val likeStatus: LikeStatus by inject()
    private val reblogStatus: ReblogStatus by inject()
    private val bookmarkStatus: BookmarkStatus by inject()

    init {
        loadTimeline()
    }

    override suspend fun handleIntentInternal(intent: TimelineIntent) {
        when (intent) {
            is TimelineIntent.LoadTimeline -> loadTimeline()
            is TimelineIntent.RefreshTimeline -> refreshTimeline()
            is TimelineIntent.LikePost -> likePost(intent.postId, intent.isLiked)
            is TimelineIntent.ReblogPost -> reblogPost(intent.postId, intent.isReblogged)
            is TimelineIntent.BookmarkPost -> bookmarkPost(intent.postId, intent.isBookmarked)
            is TimelineIntent.CreatePost -> createPost(intent.content, intent.visibility)
            is TimelineIntent.NavigateToSettings -> {
                sendEffect(TimelineEffect.NavigateToSettings)
            }
        }
    }

    private fun loadTimeline() {
        updateState { copy(isLoading = true, error = null) }

        homeTimelineRepository
            .read(FeedType.Home, refresh = false)
            .onEach { response ->
                when (response) {
                    is StoreReadResponse.Data -> {
                        val feedItems =
                            response.value
                                .map { status ->
                                    FeedItemState(
                                        id = status.remoteId,
                                        userAvatarUrl = status.avatarUrl,
                                        date = status.createdAt,
                                        username = status.userName,
                                        acctAddress = status.accountAddress,
                                        message = status.content,
                                        images = persistentListOf(),
                                        videoUrl = null,
                                        isLiked = false, // TODO: Get from status when available
                                        isReblogged = false, // TODO: Get from status when available
                                        isBookmarked = false, // TODO: Get from status when available
                                        likesCount = status.favoritesCount.toInt(), // Using favoritesCount from StatusLocal
                                        reblogsCount = status.reblogsCount.toInt(), // Using reblogsCount from StatusLocal
                                    )
                                }.toImmutableList()

                        updateState {
                            copy(
                                feedItems = feedItems,
                                isLoading = false,
                                error = null,
                            )
                        }
                    }
                    is StoreReadResponse.Error.Exception -> {
                        updateState {
                            copy(
                                isLoading = false,
                                error = response.error.message ?: "Unknown error",
                            )
                        }
                    }
                    is StoreReadResponse.Error.Message -> {
                        updateState {
                            copy(
                                isLoading = false,
                                error = response.message,
                            )
                        }
                    }
                    is StoreReadResponse.Loading -> {
                        updateState { copy(isLoading = true) }
                    }
                    is StoreReadResponse.NoNewData -> {
                        updateState { copy(isLoading = false) }
                    }
                    else -> {
                        // Handle any other cases
                    }
                }
            }.launchIn(viewModelScope)
    }

    private fun refreshTimeline() {
        homeTimelineRepository
            .read(FeedType.Home, refresh = true)
            .onEach { response ->
                // Handle response same as loadTimeline
            }.launchIn(viewModelScope)
    }

    private suspend fun likePost(
        postId: String,
        isLiked: Boolean,
    ) {
        try {
            val result = likeStatus(postId, isLiked)
            result
                .onSuccess { status ->
                    updatePostInState(postId) { post ->
                        post.copy(
                            isLiked = status.favourited ?: false,
                            likesCount = status.favouritesCount ?: 0,
                        )
                    }
                    sendEffect(TimelineEffect.ShowMessage("Post ${if (isLiked) "liked" else "unliked"}"))
                }.onFailure { error ->
                    sendEffect(TimelineEffect.ShowError("Failed to ${if (isLiked) "like" else "unlike"} post: ${error.message}"))
                }
        } catch (e: Exception) {
            sendEffect(TimelineEffect.ShowError("Failed to ${if (isLiked) "like" else "unlike"} post: ${e.message}"))
        }
    }

    private suspend fun reblogPost(
        postId: String,
        isReblogged: Boolean,
    ) {
        try {
            val result = reblogStatus(postId, isReblogged)
            result
                .onSuccess { status ->
                    updatePostInState(postId) { post ->
                        post.copy(
                            isReblogged = status.reblogged ?: false,
                            reblogsCount = status.reblogsCount ?: 0,
                        )
                    }
                    sendEffect(TimelineEffect.ShowMessage("Post ${if (isReblogged) "reblogged" else "unreblogged"}"))
                }.onFailure { error ->
                    sendEffect(TimelineEffect.ShowError("Failed to ${if (isReblogged) "reblog" else "unreblog"} post: ${error.message}"))
                }
        } catch (e: Exception) {
            sendEffect(TimelineEffect.ShowError("Failed to ${if (isReblogged) "reblog" else "unreblog"} post: ${e.message}"))
        }
    }

    private suspend fun bookmarkPost(
        postId: String,
        isBookmarked: Boolean,
    ) {
        try {
            val result = bookmarkStatus(postId, isBookmarked)
            result
                .onSuccess { status ->
                    updatePostInState(postId) { post ->
                        post.copy(isBookmarked = status.bookmarked ?: false)
                    }
                    sendEffect(TimelineEffect.ShowMessage("Post ${if (isBookmarked) "bookmarked" else "unbookmarked"}"))
                }.onFailure { error ->
                    sendEffect(
                        TimelineEffect.ShowError("Failed to ${if (isBookmarked) "bookmark" else "unbookmark"} post: ${error.message}"),
                    )
                }
        } catch (e: Exception) {
            sendEffect(TimelineEffect.ShowError("Failed to ${if (isBookmarked) "bookmark" else "unbookmark"} post: ${e.message}"))
        }
    }

    private suspend fun createPost(
        content: String,
        visibility: String,
    ) {
        updateState { copy(isCreatingPost = true) }

        try {
            val result = postStatus(content = content, visibility = visibility)
            result
                .onSuccess { status ->
                    updateState { copy(isCreatingPost = false) }
                    sendEffect(TimelineEffect.ShowMessage("Post created successfully"))
                    // Refresh timeline to show new post
                    refreshTimeline()
                }.onFailure { error ->
                    updateState { copy(isCreatingPost = false) }
                    sendEffect(TimelineEffect.ShowError("Failed to create post: ${error.message}"))
                }
        } catch (e: Exception) {
            updateState { copy(isCreatingPost = false) }
            sendEffect(TimelineEffect.ShowError("Failed to create post: ${e.message}"))
        }
    }

    private fun updatePostInState(
        postId: String,
        update: (FeedItemState) -> FeedItemState,
    ) {
        updateState {
            copy(
                feedItems =
                    feedItems
                        .map { post ->
                            if (post.id == postId) update(post) else post
                        }.toImmutableList(),
            )
        }
    }
}

/**
 * Timeline Intents (User Actions)
 */
sealed interface TimelineIntent : MviIntent {
    object LoadTimeline : TimelineIntent

    object RefreshTimeline : TimelineIntent

    data class LikePost(
        val postId: String,
        val isLiked: Boolean,
    ) : TimelineIntent

    data class ReblogPost(
        val postId: String,
        val isReblogged: Boolean,
    ) : TimelineIntent

    data class BookmarkPost(
        val postId: String,
        val isBookmarked: Boolean,
    ) : TimelineIntent

    data class CreatePost(
        val content: String,
        val visibility: String = "public",
    ) : TimelineIntent

    object NavigateToSettings : TimelineIntent
}

/**
 * Timeline State (UI State)
 */
data class TimelineState(
    val feedItems: ImmutableList<FeedItemState> = persistentListOf(),
    val isLoading: Boolean = false,
    val isCreatingPost: Boolean = false,
    val error: String? = null,
) : MviState

/**
 * Timeline Effects (One-time events)
 */
sealed interface TimelineEffect : MviEffect {
    object NavigateToSettings : TimelineEffect

    data class ShowMessage(
        val message: String,
    ) : TimelineEffect

    data class ShowError(
        val error: String,
    ) : TimelineEffect
}

/**
 * Feed Item State
 */
data class FeedItemState(
    val id: String,
    val userAvatarUrl: String,
    val date: String,
    val username: String,
    val acctAddress: String,
    val message: String?,
    val images: ImmutableList<String>,
    val videoUrl: String?,
    val isLiked: Boolean = false,
    val isReblogged: Boolean = false,
    val isBookmarked: Boolean = false,
    val likesCount: Int = 0,
    val reblogsCount: Int = 0,
)
