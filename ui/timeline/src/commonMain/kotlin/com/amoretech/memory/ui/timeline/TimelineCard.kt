package com.amoretech.memory.ui.timeline

// import androidx.compose.desktop.ui.tooling.preview.Preview
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.amoretech.memory.ui.common.composables.UserAvatar
import com.amoretech.memory.ui.common.theme.MemoryTheme
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

/**
 * Stateful
 */
@Composable
fun TimelineCard(
    state: FeedItemState,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(8.dp),
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = contentColorFor(backgroundColor),
) {
    TimelineCard(
        userAvatarUrl = state.userAvatarUrl,
        date = state.date,
        username = state.username,
        userAddress = state.acctAddress,
        toot = state.message,
        videoUrl = state.videoUrl,
        images = state.images,
        contentPadding = contentPadding,
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentColor = contentColor,
    )
}

/**
 * Stateless
 */
@Composable
fun TimelineCard(
    userAvatarUrl: String,
    date: String,
    username: String,
    userAddress: String,
    toot: String?,
    videoUrl: String?,
    images: ImmutableList<String>,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(8.dp),
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = contentColorFor(backgroundColor),
) {
    Card(
        modifier = modifier,
        colors =
            CardDefaults.cardColors(
                containerColor = backgroundColor,
                contentColor = contentColor,
            ),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(contentPadding),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            UserAvatar(
                modifier =
                    Modifier
                        .size(72.dp)
                        .clip(CircleShape),
                url = userAvatarUrl,
            )

            HorizontalSpacer()

            TootContent(
                modifier = Modifier.weight(1f).wrapContentHeight(),
                username = username,
                userAddress = userAddress,
                message = toot,
                date = date,
                images = images,
                videoUrl = videoUrl,
            )
        }
    }
}

// FeedItemState is now defined in TimelineViewModel.kt

val dummyFeedItem =
    FeedItemState(
        id = "1",
        userAvatarUrl = "https://media.mastodon.cloud/accounts/avatars/000/018/251/original/e78973b0b821c7e3.jpg",
        date = "1d",
        username = "Benjamin Stürmer",
        acctAddress = "@<EMAIL>",
        message = "\uD83D\uDC4BHello #AndroidDev",
        videoUrl = null,
        images = persistentListOf(),
        isLiked = false,
        isReblogged = false,
        isBookmarked = false,
        likesCount = 42,
        reblogsCount = 12,
    )

// @Preview
@Composable
private fun PreviewFeedCardLight() {
    MemoryTheme(useDarkTheme = false) {
        Box(Modifier.padding(12.dp)) {
            TimelineCard(
                state = dummyFeedItem,
                modifier = Modifier.wrapContentSize(),
            )
        }
    }
}

// @Preview
@Composable
private fun PreviewFeedCardDark() {
    MemoryTheme(useDarkTheme = true) {
        Box(Modifier.padding(12.dp)) {
            TimelineCard(
                state = dummyFeedItem,
                modifier = Modifier.wrapContentSize(),
            )
        }
    }
}
