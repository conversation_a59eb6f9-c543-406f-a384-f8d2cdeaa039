package com.amoretech.memory.ui.timeline.navigation

import com.amoretech.memory.ui.timeline.FeedItemState
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.flow.StateFlow
import org.mobilenativefoundation.store.store5.StoreReadResponse

/**
 * The base component describing all business logic needed for the timeline view
 */
interface TimelineComponent {
    val state: StateFlow<StoreReadResponse<ImmutableList<FeedItemState>>>
}
