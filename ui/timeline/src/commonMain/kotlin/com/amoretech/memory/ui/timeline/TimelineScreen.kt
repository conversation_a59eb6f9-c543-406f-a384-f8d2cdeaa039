package com.amoretech.memory.ui.timeline

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Repeat
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.outlined.FavoriteBorder
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.collections.immutable.ImmutableList
import org.koin.compose.koinInject

/**
 * Timeline screen with MVI pattern
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimelineScreen(
    modifier: Modifier = Modifier,
    onNavigateToSettings: () -> Unit,
    viewModel: TimelineViewModel = koinInject(),
) {
    val state by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Handle effects
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is TimelineEffect.NavigateToSettings -> {
                    onNavigateToSettings()
                }
                is TimelineEffect.ShowMessage -> {
                    snackbarHostState.showSnackbar(effect.message)
                }
                is TimelineEffect.ShowError -> {
                    snackbarHostState.showSnackbar(effect.error)
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Timeline") },
                actions = {
                    IconButton(
                        onClick = { viewModel.handleIntent(TimelineIntent.NavigateToSettings) },
                    ) {
                        Icon(Icons.Default.Settings, contentDescription = "Settings")
                    }
                },
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        modifier = modifier,
    ) { paddingValues ->
        TimelineContent(
            feedItems = state.feedItems,
            isLoading = state.isLoading,
            error = state.error,
            onLikePost = { postId, isLiked ->
                viewModel.handleIntent(TimelineIntent.LikePost(postId, isLiked))
            },
            onReblogPost = { postId, isReblogged ->
                viewModel.handleIntent(TimelineIntent.ReblogPost(postId, isReblogged))
            },
            onBookmarkPost = { postId, isBookmarked ->
                viewModel.handleIntent(TimelineIntent.BookmarkPost(postId, isBookmarked))
            },
            onRefresh = {
                viewModel.handleIntent(TimelineIntent.RefreshTimeline)
            },
            modifier = Modifier.padding(paddingValues),
        )
    }
}

@Composable
private fun TimelineContent(
    feedItems: ImmutableList<FeedItemState>,
    isLoading: Boolean,
    error: String?,
    onLikePost: (String, Boolean) -> Unit,
    onReblogPost: (String, Boolean) -> Unit,
    onBookmarkPost: (String, Boolean) -> Unit,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier =
            modifier
                .background(Color(0xFF292C36))
                .padding(8.dp)
                .fillMaxSize(),
    ) {
        when {
            isLoading && feedItems.isEmpty() -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    CircularProgressIndicator()
                }
            }
            error != null && feedItems.isEmpty() -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                    )
                }
            }
            else -> {
                LazyColumn {
                    items(feedItems, key = { item -> item.id }) { feedItem ->
                        TimelineCardWithActions(
                            state = feedItem,
                            onLike = { onLikePost(feedItem.id, !feedItem.isLiked) },
                            onReblog = { onReblogPost(feedItem.id, !feedItem.isReblogged) },
                            onBookmark = { onBookmarkPost(feedItem.id, !feedItem.isBookmarked) },
                            modifier = Modifier.wrapContentSize(),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TimelineCardWithActions(
    state: FeedItemState,
    onLike: () -> Unit,
    onReblog: () -> Unit,
    onBookmark: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        // Use the existing TimelineCard from TimelineCard.kt
        TimelineCard(
            userAvatarUrl = state.userAvatarUrl,
            date = state.date,
            username = state.username,
            userAddress = state.acctAddress,
            toot = state.message,
            videoUrl = state.videoUrl,
            images = state.images,
        )

        // Add action buttons below the card
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
        ) {
            IconButton(onClick = onLike) {
                Icon(
                    imageVector = if (state.isLiked) Icons.Filled.Favorite else Icons.Outlined.FavoriteBorder,
                    contentDescription = "Like",
                    tint = if (state.isLiked) Color.Red else Color.Gray,
                )
            }

            IconButton(onClick = onReblog) {
                Icon(
                    imageVector = Icons.Default.Repeat,
                    contentDescription = "Reblog",
                    tint = if (state.isReblogged) Color.Green else Color.Gray,
                )
            }

            IconButton(onClick = onBookmark) {
                Icon(
                    imageVector = Icons.Default.Favorite, // TODO: Use bookmark icon
                    contentDescription = "Bookmark",
                    tint = if (state.isBookmarked) Color.Blue else Color.Gray,
                )
            }
        }
    }
}
