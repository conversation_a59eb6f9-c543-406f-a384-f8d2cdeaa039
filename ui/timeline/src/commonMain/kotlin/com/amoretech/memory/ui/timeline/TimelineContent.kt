package com.amoretech.memory.ui.timeline

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.amoretech.memory.ui.common.theme.MemoryTheme
import com.amoretech.memory.ui.timeline.navigation.TimelineComponent
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.StateFlow
import org.mobilenativefoundation.store.store5.StoreReadResponse

/**
 * Timeline view that delegates business/navigation logic to [TimelineComponent]
 * for when a user wants to view their Timeline
 */
@Composable
fun TimelineContent(
    state: StateFlow<StoreReadResponse<ImmutableList<FeedItemState>>>,
    modifier: Modifier = Modifier,
) {
    when (val items = state.collectAsState().value) {
        is StoreReadResponse.Data -> {
            TimelineContent(
                feedItems = items.value,
                modifier = modifier,
            )
        }

        else -> {
            // handle error/loading
        }
    }
}

@Composable
fun TimelineContent(
    feedItems: ImmutableList<FeedItemState>,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier =
            modifier
                .background(Color(0xFF292C36))
                .padding(8.dp)
                .fillMaxSize(),
    ) {
        LazyColumn {
            items(feedItems, key = { item -> item.id }) { state ->
                TimelineCard(
                    state = state,
                    modifier = Modifier.wrapContentSize(),
                )
            }
        }
    }
}

// skip preview to work with multiplatform
// https://github.com/JetBrains/compose-jb/issues/1603
// @Preview
@Composable
private fun TimelinePreview() {
    MemoryTheme {
        TimelineContent(persistentListOf(dummyFeedItem))
    }
}
