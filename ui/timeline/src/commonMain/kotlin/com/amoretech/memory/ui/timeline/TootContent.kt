package com.amoretech.memory.ui.timeline

// import androidx.compose.desktop.ui.tooling.preview.Preview
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import com.amoretech.memory.ui.common.theme.MemoryTheme
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun TootContent(
    username: String,
    userAddress: String,
    message: String?,
    date: String,
    videoUrl: String?,
    images: ImmutableList<String>,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        TootContentHeader(
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            username = username,
            userAddress = userAddress,
            date = date,
        )
        VerticalSpacer()
        // TODO Add support for video + multiple images rendering
        // for now just show message from toot
        if (message != null) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text =
                    buildAnnotatedString {
                        append(message)
                    },
                style = MaterialTheme.typography.bodySmall,
            )
            VerticalSpacer()
        }
    }
}

// @Preview
@Composable
private fun PreviewTootContentLight() {
    MemoryTheme(useDarkTheme = false) {
        Surface {
            TootContent(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
                username = "@Omid",
                userAddress = "@<EMAIL>",
                message = "\uD83D\uDC4BHello #AndroidDev",
                date = "1d",
                images = persistentListOf(),
                videoUrl = null,
            )
        }
    }
}

// @Preview
@Composable
private fun PreviewTootContentDark() {
    MemoryTheme(useDarkTheme = true) {
        Surface {
            TootContent(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
                username = "@Omid",
                userAddress = "@<EMAIL>",
                message = "\uD83D\uDC4BHello #AndroidDev",
                date = "1d",
                images = persistentListOf(),
                videoUrl = null,
            )
        }
    }
}
