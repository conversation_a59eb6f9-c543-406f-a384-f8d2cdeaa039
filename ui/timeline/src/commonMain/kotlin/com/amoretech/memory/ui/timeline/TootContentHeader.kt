package com.amoretech.memory.ui.timeline

// import androidx.compose.desktop.ui.tooling.preview.Preview
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import com.amoretech.memory.ui.common.theme.MemoryTheme

@Composable
fun TootContentHeader(
    username: String,
    userAddress: String,
    date: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = username,
            fontWeight = FontWeight.Bold,
            style = MaterialTheme.typography.titleMedium,
        )

        Text(
            text = userAddress,
            style = MaterialTheme.typography.bodySmall,
        )

        HorizontalSpacer()

        Text(
            text = date,
            fontWeight = FontWeight.SemiBold,
            style = MaterialTheme.typography.labelSmall,
        )
    }
}

// @Preview
@Composable
private fun PreviewLight() {
    MemoryTheme(useDarkTheme = false) {
        Surface {
            TootContentHeader(
                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                username = "@Omid",
                userAddress = "@<EMAIL>",
                date = "1d",
            )
        }
    }
}

// @Preview
@Composable
private fun PreviewDark() {
    MemoryTheme(useDarkTheme = true) {
        Surface {
            TootContentHeader(
                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                username = "@Omid",
                userAddress = "@<EMAIL>",
                date = "1d",
            )
        }
    }
}
