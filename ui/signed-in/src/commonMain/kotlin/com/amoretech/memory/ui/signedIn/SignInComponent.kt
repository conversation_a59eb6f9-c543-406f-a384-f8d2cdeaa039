package com.amoretech.memory.ui.signedIn

import kotlinx.coroutines.flow.StateFlow

/**
 * The base component describing all business logic needed for the sign up or sign in screen
 */
interface SignInComponent {
    fun onCloseClicked()

    fun shouldCancelLoadingUrl(url: String): Boolean

    fun onErrorFromOAuth(error: String)

    val state: StateFlow<State>

    data class State(
        val server: String,
        val oauthAuthorizeUrl: String,
        val redirectUri: String,
        val error: String? = null,
    )
}
