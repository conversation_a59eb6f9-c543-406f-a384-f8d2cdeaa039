package com.amoretech.memory.ui.signedIn

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Suppress("ktlint:standard:function-naming")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SignInContent(
    component: SignInComponent,
    modifier: Modifier = Modifier,
) {
    val state by component.state.collectAsState()

    Column(
        modifier = modifier,
    ) {
        TopAppBar(
            modifier = Modifier.fillMaxWidth(),
            title = {
                Text("Login")
            },
            navigationIcon = {
                Icon(
                    modifier =
                        Modifier.clickable {
                            component.onCloseClicked()
                        },
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                )
            },
        )

        AnimatedVisibility(state.error != null) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .align(Alignment.CenterHorizontally)
                        .padding(16.dp)
                        .background(color = MaterialTheme.colorScheme.error.copy(alpha = 0.5F))
                        .border(
                            border =
                                BorderStroke(
                                    width = 2.dp,
                                    color =
                                        MaterialTheme.colorScheme.error,
                                ),
                            shape = MaterialTheme.shapes.small,
                        ).padding(16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    "Something is wrong!",
                    color = MaterialTheme.colorScheme.onError,
                    style = MaterialTheme.typography.bodySmall,
                )
            }
        }
        if (state.oauthAuthorizeUrl.isNotEmpty()) {
            SignInWebView(
                url = state.oauthAuthorizeUrl,
                modifier = Modifier.fillMaxSize(),
                shouldCancelLoadingUrl = component::shouldCancelLoadingUrl,
                onWebError = component::onErrorFromOAuth,
                onCancel = component::onCloseClicked,
            )
        }
    }
}
