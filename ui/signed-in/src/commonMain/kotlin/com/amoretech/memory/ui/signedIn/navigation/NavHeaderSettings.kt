package com.amoretech.memory.ui.signedIn.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * Represents a different state of navigation header (title, actions) based on active route
 */
internal sealed class NavHeaderSettings(
    val title: String = "",
    val headerAction: HeaderActionWithIcon? = null,
) {
    object Empty : NavHeaderSettings("")

    object Settings : NavHeaderSettings("Settings")

    data class Timeline(
        val action: () -> Unit,
    ) : NavHeaderSettings(
            "Timeline",
            headerAction = HeaderActionWithIcon(icon = Icons.Default.Settings, action = action),
        )
}

internal data class HeaderActionWithIcon(
    val icon: ImageVector,
    val action: () -> Unit,
)
