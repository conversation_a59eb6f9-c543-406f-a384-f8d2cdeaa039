package com.amoretech.memory.ui.signedIn

import com.amoretech.memory.domain.authentication.usecase.CreateAccessToken
import com.amoretech.memory.ui.common.mvi.MviEffect
import com.amoretech.memory.ui.common.mvi.MviIntent
import com.amoretech.memory.ui.common.mvi.MviState
import com.amoretech.memory.ui.common.mvi.MviViewModel
import io.ktor.http.Url
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Sign In ViewModel with MVI pattern
 */
class SignInViewModel :
    MviViewModel<SignInIntent, SignInState, SignInEffect>(
        initialState = SignInState(),
    ),
    KoinComponent {
    private val createAccessToken: CreateAccessToken by inject()

    override suspend fun handleIntentInternal(intent: SignInIntent) {
        when (intent) {
            is SignInIntent.InitializeSignIn -> {
                initializeSignIn(intent.server, intent.oauthUrl, intent.redirectUri)
            }
            is SignInIntent.HandleOAuthCallback -> {
                handleOAuthCallback(intent.url)
            }
            is SignInIntent.CancelSignIn -> {
                sendEffect(SignInEffect.NavigateBack)
            }
            is SignInIntent.HandleWebError -> {
                updateState { copy(error = intent.error) }
            }
        }
    }

    private fun initializeSignIn(
        server: String,
        oauthUrl: String,
        redirectUri: String,
    ) {
        updateState {
            copy(
                server = server,
                oauthAuthorizeUrl = oauthUrl,
                redirectUri = redirectUri,
                isLoading = false,
            )
        }
    }

    private suspend fun handleOAuthCallback(url: String) {
        val parsedUrl = Url(url)
        val query = parsedUrl.encodedQuery

        if (!url.contains(currentState.redirectUri) || query.isNullOrEmpty()) {
            return
        }

        when {
            query.contains("error=") -> {
                val error = query.substringAfter("error=").substringBefore("&")
                updateState { copy(error = error) }
            }
            query.contains("code=") -> {
                val code = query.substringAfter("code=").substringBefore("&")
                updateState { copy(isLoading = true, error = null) }

                try {
                    val success =
                        createAccessToken(
                            authCode = code,
                            server = currentState.server,
                        )

                    if (success) {
                        updateState { copy(isLoading = false) }
                        sendEffect(SignInEffect.NavigateToTimeline)
                    } else {
                        updateState {
                            copy(
                                isLoading = false,
                                error = "Failed to create access token",
                            )
                        }
                    }
                } catch (e: Exception) {
                    updateState {
                        copy(
                            isLoading = false,
                            error = "Failed to create access token: ${e.message}",
                        )
                    }
                }
            }
        }
    }
}

/**
 * Sign In Intents (User Actions)
 */
sealed interface SignInIntent : MviIntent {
    data class InitializeSignIn(
        val server: String,
        val oauthUrl: String,
        val redirectUri: String,
    ) : SignInIntent

    data class HandleOAuthCallback(
        val url: String,
    ) : SignInIntent

    data class HandleWebError(
        val error: String,
    ) : SignInIntent

    data object CancelSignIn : SignInIntent
}

/**
 * Sign In State (UI State)
 */
data class SignInState(
    val server: String = "",
    val oauthAuthorizeUrl: String = "",
    val redirectUri: String = "",
    val isLoading: Boolean = true,
    val error: String? = null,
) : MviState

/**
 * Sign In Effects (One-time events)
 */
sealed interface SignInEffect : MviEffect {
    data object NavigateToTimeline : SignInEffect

    data object NavigateBack : SignInEffect
}
