package com.amoretech.memory.ui.createaccount.view

import ContentWithMessageBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.amoretech.memory.ui.createaccount.event.CreateAccountEvent
import com.amoretech.memory.ui.createaccount.viewmodel.CreateAccountViewModel
import org.koin.compose.koinInject
import rememberMessageBarState

@Suppress("ktlint:standard:function-naming")
@Composable
fun AccountHandleScreen(
    viewModel: CreateAccountViewModel = koinInject(),
    domain: String,
    navigateToAccountHandle: (domain: String) -> Unit,
    onNavigateBack: () -> Unit,
) {
    val state by viewModel.uiState.collectAsState()
    val messageBarState = rememberMessageBarState()

    LaunchedEffect(domain) {
        viewModel.handleIntent(CreateAccountEvent.UpdateDomainString(domain))
    }

    ContentWithMessageBar(
        messageBarState = messageBarState,
        position = MessageBarPosition.BOTTOM,
        visibilityDuration = 1500L,
    ) {
        CreateAccountContent(
            state = state,
            onEvent = viewModel::handleIntent,
            onBackClick = onNavigateBack,
            onCloseClick = onNavigateBack,
        )
    }
}
