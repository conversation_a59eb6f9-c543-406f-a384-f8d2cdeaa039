package com.amoretech.memory.ui.createaccount.viewmodel

import com.amoretech.memory.data.persistence.setting.AppSetting
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.kotlinUtils.isValidEmail
import com.amoretech.memory.register.usecase.CreateUserAccountUseCase
import com.amoretech.memory.serverlist.usecase.GetAccessTokenUseCase
import com.amoretech.memory.ui.common.mvi.MviViewModel
import com.amoretech.memory.ui.createaccount.effect.CreateAccountEffect
import com.amoretech.memory.ui.createaccount.event.CreateAccountEvent
import com.amoretech.memory.ui.createaccount.state.CreateAccountViewState
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class CreateAccountViewModel :
    MviViewModel<CreateAccountEvent, CreateAccountViewState, CreateAccountEffect>(
        initialState = CreateAccountViewState(),
    ),
    KoinComponent {
    private val createUserAccountUseCase: CreateUserAccountUseCase by inject()
    private val getAccessTokenUseCase: GetAccessTokenUseCase by inject()
    private val appSetting: AppSetting by inject()

    init {
    }

    override suspend fun handleIntentInternal(intent: CreateAccountEvent) {
        when (intent) {
            is CreateAccountEvent.ConfirmPasswordChanged -> {
                updateConfirmPassword(intent.confirmPassword)
            }
            is CreateAccountEvent.EmailChanged -> {
                updateEmail(intent.email)
            }
            is CreateAccountEvent.FullNameChanged -> {
                updateFullName(intent.name)
            }
            is CreateAccountEvent.PasswordChanged -> {
                updatePassword(intent.password)
            }
            is CreateAccountEvent.UpdateDomainString -> {
                updateDomainString(intent.domain)
            }

            CreateAccountEvent.Submit -> {
                submit()
            }
        }
    }

    private fun updateDomainString(domain: String) {
        viewModelScope.launch {
            updateState { copy(domain = domain) }
        }
    }

    private fun updateFullName(name: String) {
        viewModelScope.launch {
            val error = if (name.isBlank()) "Full name cannot be empty" else null
            updateState { copy(fullName = name, fullNameError = error) }
        }
    }

    private fun updateEmail(email: String) {
        viewModelScope.launch {
            val error = if (!isValidEmail(email)) "Invalid email address" else null
            updateState { copy(email = email, emailError = error) }
        }
    }

    private fun updatePassword(password: String) {
        viewModelScope.launch {
            val passwordError =
                when {
                    password.length < 8 -> "Password must be at least 8 characters"
                    else -> null
                }
            val confirmPasswordError =
                if (currentState.confirmPassword.isNotEmpty() && password != currentState.confirmPassword) {
                    "Passwords do not match"
                } else {
                    null
                }
            updateState {
                copy(
                    password = password,
                    passwordError = passwordError,
                    confirmPasswordError = confirmPasswordError,
                )
            }
        }
    }

    private fun updateConfirmPassword(confirmPassword: String) {
        viewModelScope.launch {
            val error = if (confirmPassword != currentState.password) "Passwords do not match" else null
            updateState { copy(confirmPassword = confirmPassword, confirmPasswordError = error) }
        }
    }

    private fun submit() {
        // to run all validations and update state to show any errors
        val isFormValid = validateForm()
        if (isFormValid) {
            viewModelScope.launch {
                updateState { copy(isCreatingAccount = true) }
                getAccessTokenUseCase(domain = currentState.domain)?.let { appTokenModel ->

                    createUserAccountUseCase(
                        domain = currentState.domain,
                        appToken = appTokenModel.accessToken,
                        userName = currentState.fullName,
                        emailAddress = currentState.email,
                        password = currentState.password,
                    ).collect { result ->
                        when (result) {
                            is ApiResult.Success -> {
                                updateState { copy(isCreatingAccount = false) }

                                // Save user registration state
                                appSetting.setUserRegistered(
                                    email = currentState.email,
                                    userName = currentState.fullName,
                                    domain = currentState.domain
                                )

                                sendEffect(CreateAccountEffect.NavigateToOobCodeScreen(currentState.email))
                            }

                            is ApiResult.Error -> {
                                updateState { copy(isCreatingAccount = false) }
                                sendEffect(CreateAccountEffect.ShowErrorMessage(result.message))
                            }
                        }
                    }
                }
            }
        }
    }

    private fun validateForm(): Boolean {
        val nameError = if (currentState.fullName.isBlank()) "Full name cannot be empty" else null
        val emailError = if (!isValidEmail(currentState.email)) "Invalid email address" else null
        val passError = if (currentState.password.length < 8) "Password must be at least 8 characters" else null
        val confirmPassError =
            when {
                currentState.confirmPassword.isBlank() -> "Confirm password cannot be empty"
                currentState.password != currentState.confirmPassword -> "Passwords do not match"
                else -> null
            }

        updateState {
            copy(
                fullNameError = nameError,
                emailError = emailError,
                passwordError = passError,
                confirmPasswordError = confirmPassError,
            )
        }

        return listOfNotNull(nameError, emailError, passError, confirmPassError).isEmpty()
    }
}
