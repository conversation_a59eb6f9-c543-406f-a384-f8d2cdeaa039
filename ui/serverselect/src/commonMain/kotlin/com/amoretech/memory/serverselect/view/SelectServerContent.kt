package com.amoretech.memory.serverselect.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.amoretech.memory.serverselect.state.SelectServerState
import com.amoretech.memory.serverselect.view.component.ServerItemCard
import com.amoretech.memory.ui.common.composables.button.MemoryButton
import com.amoretech.memory.ui.common.composables.text.MemoryTextField
import com.amoretech.memory.ui.common.theme.MemoryTheme
import com.amoretech.memory.ui.common.theme.padding

@Composable
@Suppress("ktlint:standard:function-naming")
fun SelectServerContent(
    modifier: Modifier = Modifier,
    viewState: SelectServerState,
    onServerChange: (String) -> Unit,
    onSelectClicked: (String) -> Unit,
) {
    val padding = MaterialTheme.padding
    val scrollState = rememberScrollState()

    Surface(
        modifier = modifier.fillMaxSize(),
    ) {
        Column(
            modifier = modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            MemoryTextField(
                modifier =
                    modifier
                        .fillMaxWidth()
                        .padding(
                            top = padding.thirtyTwo,
                            start = padding.sixteen,
                            end = padding.sixteen,
                        ),
                value = viewState.server,
                onValueChange = onServerChange,
            )

            Spacer(Modifier.height(24.dp))

            MemoryButton(
                text = if (viewState.isLoading) "Connecting..." else "Select",
                enabled = !viewState.isLoading && viewState.server.isNotBlank(),
                onClick = { onSelectClicked(viewState.server) },
            )

            viewState.error?.let { errorMessage ->
                Spacer(Modifier.height(16.dp))
                androidx.compose.material3.Text(
                    text = errorMessage,
                    color = androidx.compose.material3.MaterialTheme.colorScheme.error,
                )
            }

            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(padding.sixteen)
                        .verticalScroll(scrollState),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                when (viewState.isLoading) {
                    true -> {
                        CircularProgressIndicator()
                    }

                    false -> {
                        viewState.serverList.forEach { server ->
                            ServerItemCard(
                                modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .padding(
                                            start = padding.eight,
                                            end = padding.eight,
                                            top = padding.four,
                                            bottom = padding.four,
                                        ),
                                item = server,
                                onClick = { item ->
                                    // TODO: Handle server selection
                                },
                            )
//                            Text(
//                                modifier =
//                                    Modifier.fillMaxWidth().padding(
//                                        start = padding.eight,
//                                        end = padding.eight,
//                                        top = padding.four,
//                                        bottom = padding.four,
//                                    ),
//                                text = server.domain,
//                            )
                        }
                    }
                }
            }
        }
    }
}

// @Preview
@Suppress("ktlint:standard:function-naming")
@Composable
private fun PreviewLandingContent() {
    MemoryTheme(true) {
        SelectServerContent(
            modifier = Modifier.fillMaxSize(),
            viewState = SelectServerState(),
            onServerChange = {},
            onSelectClicked = {},
        )
    }
}
