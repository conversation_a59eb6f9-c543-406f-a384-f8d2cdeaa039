package com.amoretech.memory.serverselect.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.amoretech.memory.serverselect.event.RuleEvent
import com.amoretech.memory.serverselect.state.RuleViewState
import com.amoretech.memory.serverselect.view.component.RuleItem
import com.amoretech.memory.ui.common.composables.button.MemoryAppBar
import com.amoretech.memory.ui.common.theme.padding

@Suppress("ktlint:standard:function-naming")
@Composable
internal fun RuleContent(
    state: RuleViewState,
    onEvent: (RuleEvent) -> Unit,
    onBackClick: () -> Unit,
    onCloseClick: () -> Unit,
) {
    val paddings = MaterialTheme.padding
    val scrollState = rememberScrollState()

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
    ) {
        Spacer(Modifier.height(paddings.twentyFour))

        // --- App Bar ---
        MemoryAppBar(
            modifier = Modifier.fillMaxWidth(),
            title = "memory",
            onBackClick = onBackClick,
            onCloseClick = onCloseClick,
        )

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(MaterialTheme.colorScheme.background)
                    .padding(vertical = paddings.sixteen, horizontal = paddings.sixteen)
                    .verticalScroll(scrollState),
        ) {
            Spacer(modifier = Modifier.height(paddings.sixteen))

            state.rules.forEach { rule ->
                RuleItem(
                    modifier = Modifier.fillMaxWidth(),
                    number = rule.id,
                    ruleText = rule.text,
                )
                Spacer(modifier = Modifier.height(paddings.eight))
            }
        }

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(
                        start = paddings.sixteen,
                        end = paddings.sixteen,
                        bottom = paddings.sixteen,
                    ),
        ) {
            Button(
                modifier = Modifier.fillMaxWidth(),
                onClick = { onEvent(RuleEvent.OnDisagreeClicked) },
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
            ) {
                Text(
                    text = "Agree",
                    style = MaterialTheme.typography.labelLarge,
                )
            }

            Spacer(modifier = Modifier.height(paddings.eight))

            Button(
                modifier = Modifier.fillMaxWidth(),
                onClick = { onEvent(RuleEvent.OnAgreeClicked(state.server)) },
                colors =
                    ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer,
                        contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
                    ),
            ) {
                Text(
                    text = "Disagree",
                    style = MaterialTheme.typography.labelLarge,
                )
            }

            Spacer(modifier = Modifier.height(paddings.sixteen))
        }
    }
}
