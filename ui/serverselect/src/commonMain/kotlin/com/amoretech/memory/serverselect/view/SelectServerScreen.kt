package com.amoretech.memory.serverselect.view

import ContentWithMessageBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.amoretech.memory.serverselect.effect.SelectServerEffect
import com.amoretech.memory.serverselect.event.SelectServerIntent
import com.amoretech.memory.serverselect.viewmodel.SelectServerViewModel
import org.koin.compose.koinInject
import rememberMessageBarState

@Suppress("ktlint:standard:function-naming")
@Composable
fun SelectServerScreen(
    viewModel: SelectServerViewModel = koinInject(),
    onNavigateToSignIn: (server: String, oauthUrl: String, redirectUri: String) -> Unit,
    navigateToCreateAccount: () -> Unit,
    onNavigateBack: () -> Unit,
) {
    val viewState by viewModel.uiState.collectAsState()
    val messageBarState = rememberMessageBarState()

    // Handle effects
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is SelectServerEffect.NavigateToSignIn -> {
                    onNavigateToSignIn(effect.server, effect.oauthUrl, effect.redirectUri)
                }

                SelectServerEffect.NavigateToCreateAccount -> navigateToCreateAccount()
                is SelectServerEffect.ShowMessage -> {
                    messageBarState.addError(message = effect.message)
                }
            }
        }
    }

    ContentWithMessageBar(
        messageBarState = messageBarState,
        position = MessageBarPosition.BOTTOM,
        visibilityDuration = 1500L,
    ) {
        SelectServerContent(
            viewState = viewState,
            onServerChange = { server ->
                viewModel.handleIntent(SelectServerIntent.UpdateServer(server))
            },
            onSelectClicked = { server ->
                viewModel.handleIntent(SelectServerIntent.SelectServer(server))
            },
        )
    }
}
