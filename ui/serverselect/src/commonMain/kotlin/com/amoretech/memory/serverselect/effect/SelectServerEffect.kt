package com.amoretech.memory.serverselect.effect

import com.amoretech.memory.ui.common.mvi.MviEffect

/**
 * Select Server Effects (One-time events)
 */
sealed interface SelectServerEffect : MviEffect {
    data class ShowMessage(
        val message: String,
    ) : SelectServerEffect

    data object NavigateToCreateAccount : SelectServerEffect

    data class NavigateToSignIn(
        val server: String,
        val oauthUrl: String,
        val redirectUri: String,
    ) : SelectServerEffect
}
