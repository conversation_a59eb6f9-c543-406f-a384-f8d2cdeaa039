package com.amoretech.memory.serverselect.viewmodel

import com.amoretech.memory.domain.authentication.usecase.AuthenticateClient
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.serverlist.model.AppTokenModel
import com.amoretech.memory.serverlist.model.InstanceRegistrationModel
import com.amoretech.memory.serverlist.usecase.CheckInstanceHasRegisterUseCase
import com.amoretech.memory.serverlist.usecase.FetchAppTokenFromServerUseCase
import com.amoretech.memory.serverlist.usecase.GetAccessTokenUseCase
import com.amoretech.memory.serverlist.usecase.GetFedIDBInstancesUseCase
import com.amoretech.memory.serverlist.usecase.RegisterInstanceUseCase
import com.amoretech.memory.serverlist.usecase.SaveAppTokenToDbUseCase
import com.amoretech.memory.serverlist.usecase.SaveInstanceRegistrationUseCase
import com.amoretech.memory.serverselect.effect.SelectServerEffect
import com.amoretech.memory.serverselect.event.SelectServerIntent
import com.amoretech.memory.serverselect.state.SelectServerState
import com.amoretech.memory.ui.common.mvi.MviViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Select Server ViewModel with MVI pattern
 */
class SelectServerViewModel :
    MviViewModel<SelectServerIntent, SelectServerState, SelectServerEffect>(
        initialState = SelectServerState(),
    ),
    KoinComponent {
    private val getServerList: GetFedIDBInstancesUseCase by inject()
    private val authenticateClient: AuthenticateClient by inject()
    private val checkInstanceHasRegisterUseCase: CheckInstanceHasRegisterUseCase by inject()
    private val saveInstanceRegistrationUseCase: SaveInstanceRegistrationUseCase by inject()
    private val registerInstanceUseCase: RegisterInstanceUseCase by inject()
    private val fetchAppTokenFromServerUseCase: FetchAppTokenFromServerUseCase by inject()
    private val saveAppTokenToDbUseCase: SaveAppTokenToDbUseCase by inject()
    private val getAccessTokenUseCase: GetAccessTokenUseCase by inject()

    init {
        updateState { copy(isLoading = true) }
        fetchServerList()
    }

    private fun fetchServerList() {
        viewModelScope.launch(Dispatchers.IO) {
            getServerList(limit = 10).collect { result ->
                when (result) {
                    is ApiResult.Error -> {
                        updateState { copy(error = result.message) }
                        updateState { copy(isLoading = false) }
                    }

                    is ApiResult.Success -> {
                        updateState { copy(serverList = result.data) }
                        updateState { copy(isLoading = false) }
                    }
                }
            }
        }
    }

    override suspend fun handleIntentInternal(intent: SelectServerIntent) {
        when (intent) {
            is SelectServerIntent.UpdateServer -> {
                updateState { copy(server = intent.server, error = null) }
            }

            is SelectServerIntent.SelectServer -> {
                selectServer(intent.server)
            }
        }
    }

    private fun selectServer(domain: String) {
        viewModelScope.launch {
            val instance = currentState.serverList.first { it.domain == domain }
            when (instance.openRegistration) {
                true -> {
                    updateState { copy(server = instance.domain) }
                    //  sendEffect(SelectServerEffect.NavigateToCreateAccount(domain))
                    sendEffect(SelectServerEffect.NavigateToRule(domain))
//                    // server registration is true
//                    when (val result = checkInstanceHasRegisterUseCase(domain)) {
//                        true -> {
//                            // server aka instance is already registered
//                            getAccessTokenUseCase(domain)?.let {
//                                if (it.domain == domain) {
//                                    sendEffect(SelectServerEffect.NavigateToCreateAccount(domain))
//                                }
//                            }
//                        }
//
//                        false -> {
//                            // server aka instance haven't registered
//                            registerTheInstance(domain)
//                        }
//                    }
                }

                false -> {
                    // server registration is false
                    sendEffect(SelectServerEffect.ShowMessage("Server registration is closed"))
                }
            }
        }
    }

    private fun registerTheInstance(domain: String) {
        viewModelScope.launch {
            registerInstanceUseCase(domain)
                .catch { e ->
                    sendEffect(SelectServerEffect.ShowMessage(e.message.orEmpty()))
                }.collectLatest { response ->
                    when (response) {
                        is ApiResult.Error -> {
                            sendEffect(SelectServerEffect.ShowMessage(response.message))
                        }

                        is ApiResult.Success -> {
                            // to save the just registered instance to local db
                            val clientId = response.data.clientId
                            val clientSecret = response.data.clientSecret
                            saveInstanceRegistrationUseCase(
                                InstanceRegistrationModel(
                                    domain = domain,
                                    clientId = clientId,
                                    clientSecret = clientSecret,
                                ),
                            )
                            // to get the app token
                            getTheAppToken(domain, clientId, clientSecret)
                        }
                    }
                }
        }
    }

    private fun getTheAppToken(
        domain: String,
        clientId: String,
        clientSecret: String,
    ) {
        viewModelScope.launch {
            fetchAppTokenFromServerUseCase(domain, clientId, clientSecret)
                .catch { e ->
                    sendEffect(SelectServerEffect.ShowMessage(e.message.orEmpty()))
                }.collectLatest { response ->
                    when (response) {
                        is ApiResult.Error -> {
                            sendEffect(SelectServerEffect.ShowMessage(response.message))
                        }

                        is ApiResult.Success -> {
                            val accessToken = response.data.accessToken
                            val tokenType = response.data.tokenType
                            val scope = response.data.scope
                            val createAt = response.data.createdAt
                            saveAppTokenToDbUseCase(
                                model =
                                    AppTokenModel(
                                        domain = domain,
                                        accessToken = accessToken,
                                        tokenType = tokenType,
                                        scope = scope,
                                        createdAt = createAt,
                                    ),
                            )
                            sendEffect(
                                SelectServerEffect.NavigateToCreateAccount(
                                    domain,
                                ),
                            )
                        }
                    }
                }
        }
    }

//    private suspend fun selectServer(server: String) {
//        if (server.isBlank()) {
//            updateState { copy(error = "Please enter a server") }
//            return
//        }
//
//        updateState { copy(isLoading = true, error = null) }
//
//        try {
//            val success =
//                authenticateClient(
//                    domain = server,
//                    clientName = "Memory Client",
//                    redirectURIs = "memory-client://oauth/callback",
//                    scopes = "read write follow push",
//                    website = null,
//                )
//
//            if (success) {
//                updateState { copy(isLoading = false) }
//                // For now, we'll create a simple OAuth URL
//                // TODO: Get the actual OAuth URL from the authentication result
//                val oauthUrl =
//                    "https://$server/oauth/authorize?client_id=CLIENT_ID&scope=read+write+follow+push&redirect_uri=memory-client://oauth/callback&response_type=code"
//                sendEffect(
//                    SelectServerEffect.NavigateToSignIn(
//                        server = server,
//                        oauthUrl = oauthUrl,
//                        redirectUri = "memory-client://oauth/callback",
//                    ),
//                )
//            } else {
//                updateState {
//                    copy(
//                        isLoading = false,
//                        error = "Failed to connect to server",
//                    )
//                }
//            }
//        } catch (e: Exception) {
//            updateState {
//                copy(
//                    isLoading = false,
//                    error = e.message ?: "Failed to connect to server",
//                )
//            }
//        }
//    }
}
