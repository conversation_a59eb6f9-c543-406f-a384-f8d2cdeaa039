package com.amoretech.memory.serverselect.viewmodel

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.usecase.GetInstanceRulesUseCase
import com.amoretech.memory.serverselect.effect.RuleEffect
import com.amoretech.memory.serverselect.event.RuleEvent
import com.amoretech.memory.serverselect.state.RuleViewState
import com.amoretech.memory.ui.common.mvi.MviViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class RuleViewModel :
    MviViewModel<RuleEvent, RuleViewState, RuleEffect>(
        initialState = RuleViewState(),
    ),
    KoinComponent {
    private val getInstanceRulesUseCase: GetInstanceRulesUseCase by inject()

    init {
        updateState { copy(isLoading = true) }
        //  getTheRulesForThisInstance(currentState.server)
    }

    override suspend fun handleIntentInternal(intent: RuleEvent) {
        when (intent) {
            is RuleEvent.OnAgreeClicked -> sendEffect(RuleEffect.NavigateToWebView(currentState.server))
            RuleEvent.OnDisagreeClicked -> sendEffect(RuleEffect.NavigateBack)
            is RuleEvent.UpdateDomainString -> updateDomainString(intent.domain)
        }
    }

    private fun updateDomainString(domain: String) {
        viewModelScope.launch {
            updateState { copy(server = domain) }
        }
        getTheRulesForThisInstance(domain)
    }

    private fun getTheRulesForThisInstance(domain: String) {
        viewModelScope.launch {
            getInstanceRulesUseCase(domain)
                .catch { e ->
                    sendEffect(RuleEffect.ShowMessage(e.message.orEmpty()))
                }.collectLatest { response ->
                    when (response) {
                        is ApiResult.Error -> {
                            sendEffect(RuleEffect.ShowMessage(response.message))
                        }

                        is ApiResult.Success -> {
                            updateState { copy(rules = response.data) }
                        }
                    }
                }
        }
    }
}
