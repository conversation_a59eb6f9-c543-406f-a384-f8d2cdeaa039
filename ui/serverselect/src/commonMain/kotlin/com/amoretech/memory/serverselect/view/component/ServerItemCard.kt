package com.amoretech.memory.serverselect.view.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.CalendarToday
import androidx.compose.material.icons.outlined.Language
import androidx.compose.material.icons.outlined.PeopleOutline
import androidx.compose.material.icons.rounded.Map
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import com.amoretech.memory.serverlist.model.FedIDBModel
import com.amoretech.memory.ui.common.theme.padding
import com.amoretech.memory.ui.common.theme.strokeWidth

@Suppress("ktlint:standard:function-naming")
@Composable
fun ServerItemCard(
    modifier: Modifier = Modifier,
    item: FedIDBModel,
    onClick: (item: FedIDBModel) -> Unit,
) {
    val paddings = MaterialTheme.padding
    val borderStrokes = MaterialTheme.strokeWidth
    // val isSelected = info.type == InstanceCardType.SELECTED
    val isSelected = if (item.id.toInt() == 18059) true else false

    val cardColors =
        if (isSelected) {
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
                contentColor = MaterialTheme.colorScheme.onSurface,
            )
        } else {
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant,
                contentColor = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }

    val border =
        if (isSelected) {
            BorderStroke(borderStrokes.two, MaterialTheme.colorScheme.primary)
        } else {
            null
        }

    Card(
        modifier = modifier.fillMaxWidth(),
        onClick = { onClick(item) },
        shape = MaterialTheme.shapes.extraLarge,
        colors = cardColors,
        border = border,
    ) {
        Column(
            modifier =
                Modifier.padding(
                    horizontal = paddings.twentyFour,
                    vertical = paddings.sixteen,
                ),
        ) {
            Text(
                text = item.domain,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.ExtraBold,
                color = if (isSelected) MaterialTheme.colorScheme.primary else cardColors.contentColor,
            )

            Spacer(modifier = Modifier.height(paddings.four))

            Text(
                text = item.description.orEmpty(),
                style = MaterialTheme.typography.bodyMedium,
                color = cardColors.contentColor.copy(alpha = 0.8f),
            )

            Spacer(modifier = Modifier.height(paddings.sixteen))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                InfoItem(
                    icon = Icons.Outlined.PeopleOutline,
                    // text = "${item.stats.userCount} ${stringResource(memory.ui.serverselect.generated.resources.Res)}",
                    text = "${item.stats.userCount} members",
                    color = cardColors.contentColor.copy(alpha = 0.7f),
                )

                InfoItem(
                    icon = Icons.Outlined.CalendarToday,
                    text = item.firstSeenAt.time.toString(),
                    color = cardColors.contentColor.copy(alpha = 0.7f),
                )
            }

            Spacer(modifier = Modifier.height(paddings.eight))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                InfoItem(
                    icon = Icons.Outlined.Language,
                    // text = stringResource(),
                    text = "Languages",
                    color = cardColors.contentColor.copy(alpha = 0.7f),
                )

                InfoItem(
                    icon = Icons.Rounded.Map,
                    text = "${item.location.city}, ${item.location.country}",
                    color = cardColors.contentColor.copy(alpha = 0.7f),
                )
            }
        }
    }
}
