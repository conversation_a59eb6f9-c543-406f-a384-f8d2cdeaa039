package com.amoretech.memory.serverselect.state

import com.amoretech.memory.serverlist.model.FedIDBModel
import com.amoretech.memory.ui.common.mvi.MviState

/**
 * Select Server State (UI State)
 */
data class SelectServerState(
    val serverList: List<FedIDBModel> = emptyList(),
    val server: String = "memory-art.xyz",
    val isLoading: Boolean = false,
    val error: String? = null,
) : MviState
