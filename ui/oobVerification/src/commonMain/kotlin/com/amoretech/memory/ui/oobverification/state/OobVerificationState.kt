package com.amoretech.memory.ui.oobverification.state

import com.amoretech.memory.ui.common.mvi.MviState

data class OobVerificationState(
    val domain: String = "",
    val email: String = "",
    val oobCode: String = "",
    val oobCodeError: String? = null,
    val isVerifying: Boolean = false,
    val isResendingCode: Boolean = false,
    val resendCooldownSeconds: Int = 0,
) : MviState {
    val canResendCode: Boolean
        get() = resendCooldownSeconds == 0 && !isResendingCode
    
    val isFormValid: Boolean
        get() = oobCode.isNotBlank() && oobCodeError == null
}
