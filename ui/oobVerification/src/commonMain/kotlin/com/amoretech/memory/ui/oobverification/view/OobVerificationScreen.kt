package com.amoretech.memory.ui.oobverification.view

import ContentWithMessageBar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.amoretech.memory.ui.oobverification.effect.OobVerificationEffect
import com.amoretech.memory.ui.oobverification.event.OobVerificationEvent
import com.amoretech.memory.ui.oobverification.state.OobVerificationState
import com.amoretech.memory.ui.oobverification.viewmodel.OobVerificationViewModel
import org.koin.compose.koinInject
import rememberMessageBarState

@Suppress("ktlint:standard:function-naming")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OobVerificationScreen(
    domain: String,
    email: String,
    onNavigateToSignIn: () -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: OobVerificationViewModel = koinInject(),
) {
    val state by viewModel.uiState.collectAsState()
    val messageBarState = rememberMessageBarState()

    // Initialize the screen with domain and email
    LaunchedEffect(domain, email) {
        viewModel.handleIntent(OobVerificationEvent.Initialize(domain, email))
    }

    // Handle effects
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is OobVerificationEffect.ShowErrorMessage -> {
                    messageBarState.addError(message = effect.message)
                }
                is OobVerificationEffect.ShowSuccessMessage -> {
                    messageBarState.addSuccess(message = effect.message)
                }
                is OobVerificationEffect.NavigateToSignIn -> {
                    onNavigateToSignIn()
                }
                is OobVerificationEffect.NavigateBack -> {
                    onNavigateBack()
                }
            }
        }
    }

    ContentWithMessageBar(messageBarState = messageBarState) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text("Verify Your Account") },
                    navigationIcon = {
                        IconButton(onClick = { viewModel.handleIntent(OobVerificationEvent.NavigateBack) }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                            )
                        }
                    },
                )
            },
        ) { paddingValues ->
            OobVerificationContent(
                state = state,
                onEvent = viewModel::handleIntent,
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .padding(16.dp),
            )
        }
    }
}

@Suppress("ktlint:standard:function-naming")
@Composable
private fun OobVerificationContent(
    state: OobVerificationState,
    onEvent: (OobVerificationEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val focusManager = LocalFocusManager.current
    val oobCodeFocusRequester = remember { FocusRequester() }

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp),
    ) {
        Spacer(modifier = Modifier.height(32.dp))

        // Header
        Text(
            text = "Check Your Email",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
        )

        // Description
        Text(
            text = "We've sent a 6-digit verification code to\n${state.email}",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )

        Spacer(modifier = Modifier.height(16.dp))

        // OOB Code Input
        OutlinedTextField(
            value = state.oobCode,
            onValueChange = { code ->
                if (code.length <= 6) {
                    onEvent(OobVerificationEvent.OobCodeChanged(code))
                }
            },
            label = { Text("Verification Code") },
            placeholder = { Text("000000") },
            isError = state.oobCodeError != null,
            supportingText = state.oobCodeError?.let { { Text(it) } },
            keyboardOptions =
                KeyboardOptions(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Done,
                ),
            keyboardActions =
                KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (state.isFormValid) {
                            onEvent(OobVerificationEvent.VerifyOobCode)
                        }
                    },
                ),
            modifier =
                Modifier
                    .fillMaxWidth()
                    .focusRequester(oobCodeFocusRequester),
            singleLine = true,
        )

        // Verify Button
        Button(
            onClick = { onEvent(OobVerificationEvent.VerifyOobCode) },
            enabled = state.isFormValid && !state.isVerifying,
            modifier = Modifier.fillMaxWidth(),
        ) {
            if (state.isVerifying) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(if (state.isVerifying) "Verifying..." else "Verify Account")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Resend Code Section
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            Text(
                text = "Didn't receive the code? ",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            if (state.canResendCode) {
                TextButton(
                    onClick = { onEvent(OobVerificationEvent.ResendOobCode) },
                    enabled = !state.isResendingCode,
                ) {
                    Text(if (state.isResendingCode) "Sending..." else "Resend")
                }
            } else {
                Text(
                    text = "Resend in ${state.resendCooldownSeconds}s",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // Server info
        Text(
            text = "Verifying account on ${state.domain}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
        )
    }

    // Auto-focus on OOB code field when screen loads
    LaunchedEffect(Unit) {
        oobCodeFocusRequester.requestFocus()
    }
}
