package com.amoretech.memory.ui.oobverification.viewmodel

import com.amoretech.memory.data.persistence.setting.AppSetting
import com.amoretech.memory.ui.common.mvi.MviViewModel
import com.amoretech.memory.ui.oobverification.effect.OobVerificationEffect
import com.amoretech.memory.ui.oobverification.event.OobVerificationEvent
import com.amoretech.memory.ui.oobverification.state.OobVerificationState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class OobVerificationViewModel :
    MviViewModel<OobVerificationEvent, OobVerificationState, OobVerificationEffect>(
        initialState = OobVerificationState(),
    ),
    KoinComponent {
    
    private val appSetting: AppSetting by inject()
    
    companion object {
        private const val RESEND_COOLDOWN_SECONDS = 60
        private const val OOB_CODE_LENGTH = 6 // Typical OOB code length
    }

    override suspend fun handleIntentInternal(intent: OobVerificationEvent) {
        when (intent) {
            is OobVerificationEvent.Initialize -> {
                initialize(intent.domain, intent.email)
            }
            is OobVerificationEvent.OobCodeChanged -> {
                updateOobCode(intent.oobCode)
            }
            is OobVerificationEvent.VerifyOobCode -> {
                verifyOobCode()
            }
            is OobVerificationEvent.ResendOobCode -> {
                resendOobCode()
            }
            is OobVerificationEvent.NavigateBack -> {
                sendEffect(OobVerificationEffect.NavigateBack)
            }
        }
    }

    private fun initialize(domain: String, email: String) {
        viewModelScope.launch {
            updateState {
                copy(
                    domain = domain,
                    email = email
                )
            }
        }
    }

    private fun updateOobCode(oobCode: String) {
        viewModelScope.launch {
            val error = validateOobCode(oobCode)
            updateState {
                copy(
                    oobCode = oobCode,
                    oobCodeError = error
                )
            }
        }
    }

    private fun validateOobCode(oobCode: String): String? {
        return when {
            oobCode.isBlank() -> "OOB code cannot be empty"
            oobCode.length != OOB_CODE_LENGTH -> "OOB code must be $OOB_CODE_LENGTH digits"
            !oobCode.all { it.isDigit() } -> "OOB code must contain only numbers"
            else -> null
        }
    }

    private fun verifyOobCode() {
        if (!currentState.isFormValid) {
            return
        }

        viewModelScope.launch {
            updateState { copy(isVerifying = true) }
            
            try {
                // TODO: Implement actual OOB verification API call
                // For now, simulate verification
                delay(2000) // Simulate network call
                
                // Save OOB code and update user state
                appSetting.setOobCodeReceived(currentState.oobCode)
                
                updateState { copy(isVerifying = false) }
                sendEffect(OobVerificationEffect.ShowSuccessMessage("Account verified successfully!"))
                sendEffect(OobVerificationEffect.NavigateToSignIn)
                
            } catch (e: Exception) {
                updateState { copy(isVerifying = false) }
                sendEffect(OobVerificationEffect.ShowErrorMessage("Verification failed: ${e.message}"))
            }
        }
    }

    private fun resendOobCode() {
        if (!currentState.canResendCode) {
            return
        }

        viewModelScope.launch {
            updateState { copy(isResendingCode = true) }
            
            try {
                // TODO: Implement actual resend OOB code API call
                delay(1000) // Simulate network call
                
                updateState { 
                    copy(
                        isResendingCode = false,
                        resendCooldownSeconds = RESEND_COOLDOWN_SECONDS
                    )
                }
                
                sendEffect(OobVerificationEffect.ShowSuccessMessage("Verification code sent to ${currentState.email}"))
                startResendCooldown()
                
            } catch (e: Exception) {
                updateState { copy(isResendingCode = false) }
                sendEffect(OobVerificationEffect.ShowErrorMessage("Failed to resend code: ${e.message}"))
            }
        }
    }

    private fun startResendCooldown() {
        viewModelScope.launch {
            repeat(RESEND_COOLDOWN_SECONDS) {
                delay(1000)
                updateState { 
                    copy(resendCooldownSeconds = resendCooldownSeconds - 1)
                }
            }
        }
    }
}
