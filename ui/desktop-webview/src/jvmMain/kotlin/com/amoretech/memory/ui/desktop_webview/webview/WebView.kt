package com.amoretech.memory.ui.desktop_webview.webview

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.awt.SwingPanel

@Composable
fun JFXWebView(
    url: String,
    shouldCancelLoadingUrl: (url: String) -> Boolean,
    modifier: Modifier = Modifier
) {
    SwingPanel(
        background = MaterialTheme.colors.surface,
        factory = {
            JFXWebView(
                url = url,
                onUrlOfCurrentPageChanged = { url ->
                    shouldCancelLoadingUrl(url)
                },
            )
        },
        modifier = modifier,
    )
}
