package com.amoretech.memory.ui.desktop_webview.webview

import javafx.application.Platform
import javafx.embed.swing.JFXPanel
import javafx.scene.Scene
import javafx.scene.paint.Color
import javafx.scene.web.WebView

internal class JFXWebView(
    private val url: String,
    private val onUrlOfCurrentPageChanged: (newUrl: String) -> Unit,
) : JFXPanel() {
    init {
        // FIXME : I added this line to avoid the following behavior:
        // If the user goes back and then restarts the webview the initialization is not done
        // may be because of Decompose navigation.pop() ??
        Platform.setImplicitExit(false)

        Platform.runLater(::initialiseJavaFXScene)
    }

    private fun initialiseJavaFXScene() {
        val webView = WebView()
        webView.pageFill = Color.TRANSPARENT
        webView.engine.locationProperty().addListener { observable, _, _ ->
            onUrlOfCurrentPageChanged(observable.value)
        }

        val scene = Scene(webView, Color.TRANSPARENT)
        setScene(scene)

        webView.engine.load(url)
    }
}
