package com.amoretech.memory.ui.webview.effect

import com.amoretech.memory.ui.common.mvi.MviEffect

/**
 * Side effects for WebView screen
 */
sealed interface WebViewEffect : MviEffect {
    data object NavigateBack : WebViewEffect
    data class ShowMessage(val message: String) : WebViewEffect
    data class LoadUrl(val url: String) : WebViewEffect
    data object Reload : WebViewEffect
    data object GoBack : WebViewEffect
    data object GoForward : WebViewEffect
    data object StopLoading : WebViewEffect
}
