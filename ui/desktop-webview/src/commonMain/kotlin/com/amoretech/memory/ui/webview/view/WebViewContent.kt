package com.amoretech.memory.ui.webview.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.amoretech.memory.ui.common.composables.button.MemoryAppBar
import com.amoretech.memory.ui.common.theme.padding
import com.amoretech.memory.ui.webview.component.WebViewComponent
import com.amoretech.memory.ui.webview.event.WebViewEvent
import com.amoretech.memory.ui.webview.state.WebViewState

@Suppress("ktlint:standard:function-naming")
@Composable
fun WebViewContent(
    state: WebViewState,
    onEvent: (WebViewEvent) -> Unit,
    onBackClick: () -> Unit,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val paddings = MaterialTheme.padding

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
    ) {
        Spacer(Modifier.height(paddings.twentyFour))

        // --- App Bar ---
        MemoryAppBar(
            modifier = Modifier.fillMaxWidth(),
            title = if (state.title.isNotEmpty()) state.title else "memory",
            onBackClick = onBackClick,
            onCloseClick = onCloseClick,
        )

        // --- Progress Bar ---
        if (state.isLoading && state.progress > 0f) {
            LinearProgressIndicator(
                progress = { state.progress },
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary,
            )
        } else if (state.isLoading) {
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary,
            )
        }

        // --- WebView Content Area (replaces the scrollable rules list) ---
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .background(MaterialTheme.colorScheme.background)
                .padding(horizontal = paddings.sixteen),
        ) {
            when {
                state.error != null -> {
                    // Error State
                    ErrorContent(
                        error = state.error,
                        onRetry = { onEvent(WebViewEvent.OnRetryClicked) },
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                state.url.isEmpty() -> {
                    // Empty State
                    EmptyContent(
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                else -> {
                    // WebView
                    WebViewComponent(
                        url = state.url,
                        modifier = Modifier.fillMaxSize(),
                        onEvent = onEvent,
                    )
                }
            }

            // Loading overlay
            if (state.isLoading && state.progress == 0f) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(48.dp),
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            }
        }

        // --- Bottom Action Buttons (similar to RuleContent) ---
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = paddings.sixteen,
                    end = paddings.sixteen,
                    bottom = paddings.sixteen,
                ),
        ) {
            // Navigation buttons row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(paddings.eight)
            ) {
                Button(
                    modifier = Modifier.weight(1f),
                    onClick = { onEvent(WebViewEvent.GoBack) },
                    enabled = state.canGoBack,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer,
                        contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
                    ),
                ) {
                    Text(
                        text = "Back",
                        style = MaterialTheme.typography.labelLarge,
                    )
                }

                Button(
                    modifier = Modifier.weight(1f),
                    onClick = { onEvent(WebViewEvent.GoForward) },
                    enabled = state.canGoForward,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer,
                        contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
                    ),
                ) {
                    Text(
                        text = "Forward",
                        style = MaterialTheme.typography.labelLarge,
                    )
                }

                Button(
                    modifier = Modifier.weight(1f),
                    onClick = { onEvent(WebViewEvent.Reload) },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                    ),
                ) {
                    Text(
                        text = "Reload",
                        style = MaterialTheme.typography.labelLarge,
                    )
                }
            }

            Spacer(modifier = Modifier.height(paddings.sixteen))
        }
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = "Failed to load page",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.error,
            textAlign = TextAlign.Center,
        )
        
        Spacer(modifier = Modifier.height(MaterialTheme.padding.eight))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
        )
        
        Spacer(modifier = Modifier.height(MaterialTheme.padding.sixteen))
        
        Button(
            onClick = onRetry,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
            ),
        ) {
            Text(
                text = "Retry",
                style = MaterialTheme.typography.labelLarge,
            )
        }
    }
}

@Composable
private fun EmptyContent(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = "No URL provided",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
        )
    }
}
