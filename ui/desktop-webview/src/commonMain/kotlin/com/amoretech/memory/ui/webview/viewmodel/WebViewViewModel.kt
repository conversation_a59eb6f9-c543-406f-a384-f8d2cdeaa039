package com.amoretech.memory.ui.webview.viewmodel

import com.amoretech.memory.ui.common.mvi.MviViewModel
import com.amoretech.memory.ui.webview.effect.WebViewEffect
import com.amoretech.memory.ui.webview.event.WebViewEvent
import com.amoretech.memory.ui.webview.state.WebViewState
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent

/**
 * ViewModel for WebView screen following MVI pattern
 */
class WebViewViewModel :
    MviViewModel<WebViewEvent, WebViewState, WebViewEffect>(
        initialState = WebViewState(),
    ),
    KoinComponent {

    override suspend fun handleIntentInternal(intent: WebViewEvent) {
        when (intent) {
            is WebViewEvent.LoadUrl -> {
                updateState { 
                    copy(
                        url = intent.url,
                        isLoading = true,
                        error = null
                    ) 
                }
                sendEffect(WebViewEffect.LoadUrl(intent.url))
            }
            
            WebViewEvent.Reload -> {
                updateState { copy(isLoading = true, error = null) }
                sendEffect(WebViewEffect.Reload)
            }
            
            WebViewEvent.GoBack -> {
                sendEffect(WebViewEffect.GoBack)
            }
            
            WebViewEvent.GoForward -> {
                sendEffect(WebViewEffect.GoForward)
            }
            
            WebViewEvent.StopLoading -> {
                updateState { copy(isLoading = false) }
                sendEffect(WebViewEffect.StopLoading)
            }
            
            is WebViewEvent.OnPageStarted -> {
                updateState { 
                    copy(
                        url = intent.url,
                        isLoading = true,
                        error = null,
                        progress = 0f
                    ) 
                }
            }
            
            is WebViewEvent.OnPageFinished -> {
                updateState { 
                    copy(
                        url = intent.url,
                        isLoading = false,
                        progress = 1f
                    ) 
                }
            }
            
            is WebViewEvent.OnProgressChanged -> {
                updateState { copy(progress = intent.progress) }
            }
            
            is WebViewEvent.OnReceivedError -> {
                updateState { 
                    copy(
                        isLoading = false,
                        error = intent.error
                    ) 
                }
            }
            
            is WebViewEvent.OnReceivedTitle -> {
                updateState { copy(title = intent.title) }
            }
            
            WebViewEvent.OnNavigateBack -> {
                sendEffect(WebViewEffect.NavigateBack)
            }
            
            WebViewEvent.OnRetryClicked -> {
                if (currentState.url.isNotEmpty()) {
                    handleIntentInternal(WebViewEvent.LoadUrl(currentState.url))
                }
            }
        }
    }

    /**
     * Initialize WebView with URL
     */
    fun initializeWithUrl(url: String) {
        viewModelScope.launch {
            handleIntentInternal(WebViewEvent.LoadUrl(url))
        }
    }
}
