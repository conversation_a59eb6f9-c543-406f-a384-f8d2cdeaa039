package com.amoretech.memory.ui.webview.state

/**
 * Represents the current state of the WebView
 */
data class WebViewState(
    val url: String = "",
    val isLoading: Boolean = false,
    val error: String? = null,
    val canGoBack: Boolean = false,
    val canGoForward: Boolean = false,
    val title: String = "",
    val progress: Float = 0f,
)

/**
 * Represents different loading states for better UX
 */
sealed class WebViewLoadingState {
    data object Idle : WebViewLoadingState()
    data object Loading : WebViewLoadingState()
    data class Error(val message: String, val canRetry: Boolean = true) : WebViewLoadingState()
    data object Success : WebViewLoadingState()
}
