package com.amoretech.memory.ui.webview.event

import com.amoretech.memory.ui.common.mvi.MviIntent

/**
 * Events that can be triggered in the WebView screen
 */
sealed interface WebViewEvent : MviIntent {
    data class LoadUrl(val url: String) : WebViewEvent
    data object Reload : WebViewEvent
    data object GoBack : WebViewEvent
    data object GoForward : WebViewEvent
    data object StopLoading : WebViewEvent
    data class OnPageStarted(val url: String) : WebViewEvent
    data class OnPageFinished(val url: String) : WebViewEvent
    data class OnProgressChanged(val progress: Float) : WebViewEvent
    data class OnReceivedError(val error: String) : WebViewEvent
    data class OnReceivedTitle(val title: String) : WebViewEvent
    data object OnNavigateBack : WebViewEvent
    data object OnRetryClicked : WebViewEvent
}
