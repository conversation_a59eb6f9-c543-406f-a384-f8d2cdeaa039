package com.amoretech.memory.ui.webview.view

import ContentWithMessageBar
import MessageBarPosition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.amoretech.memory.ui.webview.effect.WebViewEffect
import com.amoretech.memory.ui.webview.event.WebViewEvent
import com.amoretech.memory.ui.webview.viewmodel.WebViewViewModel
import org.koin.compose.koinInject
import rememberMessageBarState

@Suppress("ktlint:standard:function-naming")
@Composable
fun WebViewScreen(
    url: String,
    onNavigateBack: () -> Unit,
    viewModel: WebViewViewModel = koinInject(),
) {
    val state by viewModel.uiState.collectAsState()
    val messageBarState = rememberMessageBarState()

    // Initialize WebView with URL
    LaunchedEffect(url) {
        if (url.isNotEmpty()) {
            viewModel.initializeWithUrl(url)
        }
    }

    // Handle effects
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                WebViewEffect.NavigateBack -> onNavigateBack()
                is WebViewEffect.ShowMessage -> {
                    messageBarState.addError(message = effect.message)
                }
                // Other effects are handled by platform-specific WebView implementations
                else -> { /* Platform-specific effects handled in WebViewComponent */ }
            }
        }
    }

    ContentWithMessageBar(
        messageBarState = messageBarState,
        position = MessageBarPosition.BOTTOM,
        visibilityDuration = 1500L,
    ) {
        WebViewContent(
            state = state,
            onEvent = viewModel::handleIntent,
            onBackClick = { viewModel.handleIntent(WebViewEvent.OnNavigateBack) },
            onCloseClick = { viewModel.handleIntent(WebViewEvent.OnNavigateBack) },
        )
    }
}
