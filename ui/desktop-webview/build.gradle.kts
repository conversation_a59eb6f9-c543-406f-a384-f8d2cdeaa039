import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.javafx)
}

javafx {
    version = libs.versions.javafxLib.get()
    modules(
        "javafx.base",
        "javafx.controls",
        "javafx.graphics",
        "javafx.swing",
        "javafx.web",
        "javafx.media"
    )
}

kotlin {

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.ui.desktop_webview"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "desktop-webviewKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(libs.kotlin.stdlib)
                implementation(libs.compose.runtime)
                implementation(libs.compose.foundation)
                implementation(libs.compose.material3)
                implementation(libs.compose.ui)
                implementation(libs.compose.ui.tooling.preview)
                implementation(libs.kotlinx.coroutines.core)

                // UI Common module for shared components
                implementation(projects.ui.common)

                // Koin for dependency injection
                implementation(libs.io.insert.koin.core)
                implementation(libs.io.insert.koin.compose)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.androidx.compose.ui.android)
                implementation(libs.androidx.webkit)
            }
        }

        iosMain {
            dependencies {}
        }

        jvmMain {
            dependencies{
                implementation(libs.compose.desktop)
                val javafxVersion = "21.0.1"
                implementation("org.openjfx:javafx-base:$javafxVersion:${getPlatform()}")
                implementation("org.openjfx:javafx-controls:$javafxVersion:${getPlatform()}")
                implementation("org.openjfx:javafx-graphics:$javafxVersion:${getPlatform()}")
                implementation("org.openjfx:javafx-swing:$javafxVersion:${getPlatform()}")
                implementation("org.openjfx:javafx-web:$javafxVersion:${getPlatform()}")
                implementation("org.openjfx:javafx-media:$javafxVersion:${getPlatform()}")

            }
        }

        jvmTest {
            dependencies{}
        }

    }

}

/**
 * Determines the current operating system using the os.name system property.
 * This is a public and stable way to check the OS from a Gradle script.
 *
 * @return A string representing the platform ("win", "mac", "linux").
 * @throws GradleException if the OS cannot be determined or is not one of the expected types.
 */
fun getPlatform(): String {
    val osName = System.getProperty("os.name").lowercase() // Get OS name and convert to lowercase for easier matching
    return when {
        osName.contains("windows") -> "win"
        osName.contains("mac") || osName.contains("darwin") -> "mac" // Check for both "mac" and "darwin"
        osName.contains("linux") -> "linux"
        else -> throw GradleException("Unknown or unsupported OS: $osName") // Throw exception for unknown OS
    }
}