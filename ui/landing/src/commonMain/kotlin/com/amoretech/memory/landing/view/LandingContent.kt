package com.amoretech.memory.landing.view

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.amoretech.memory.ui.common.myiconpack.Memory
import com.amoretech.memory.ui.common.myiconpack.MyIconPack
import com.amoretech.memory.ui.common.myiconpack.Text
import com.amoretech.memory.ui.common.theme.MemoryTheme

// @Suppress("ktlint:standard:function-naming")

/**
 * Landing view that delegates business logic to [LandingContent]
 */
// @Composable
// fun LandingContentTop(
//    modifier: Modifier = Modifier,
//    onGetStartedClicked: () -> Unit,
//    appIcon: @Composable () -> Unit = {
//        AsyncImage(
//            modifier =
//                Modifier
//                    .padding(horizontal = 48.dp)
//                    .size(240.dp)
//                    .clip(CircleShape),
//            contentScale = ContentScale.Crop,
//            contentDescription = "App Logo",
//            model = "memory logo here",
//        )
//    },
// ) {
//    LandingContent(
//        modifier = modifier,
//        onGetStartedClicked = onGetStartedClicked,
//        appIcon = appIcon,
//    )
// }

@Suppress("ktlint:standard:function-naming")
@Composable
fun LandingContent(
    modifier: Modifier = Modifier,
    navigateToSelectServer: () -> Unit,
    navigateToLogin: () -> Unit,
    appIcon: @Composable () -> Unit = {
        AsyncImage(
            modifier =
                Modifier
                    .padding(horizontal = 48.dp)
                    .size(240.dp)
                    .clip(CircleShape),
            contentScale = ContentScale.Crop,
            contentDescription = "App Logo",
            model = "memory logo here",
        )
    },
) {
    Surface(
        modifier = modifier,
        color = MaterialTheme.colorScheme.surface,
    ) {
        GlowingGradientBackground {
            Column(
                modifier = Modifier.fillMaxSize(),
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Spacer(
                        modifier = Modifier.height(30.dp),
                    )
                    MemoryTextIcon(
                        modifier =
                            Modifier
                                .align(
                                    Alignment.CenterHorizontally,
                                ).padding(
                                    top = 24.dp,
                                    bottom = 18.dp,
                                ),
                    )
                }

                Spacer(
                    modifier = Modifier.height(200.dp),
                )

                Text(
                    text = "Welcome to",
                    style =
                        TextStyle(
                            fontSize = 28.sp,
                            fontWeight = FontWeight(400),
                            color = Color.White,
                        ),
                    modifier =
                        Modifier.padding(
                            top = 16.dp,
                            start = 18.dp,
                            end = 16.dp,
                        ),
                )
                MemoryMainTextIcon()
                Text(
                    text = "The most customisable social app you’ve ever experienced, in the palm of your hand.",
                    modifier = Modifier.padding(16.dp),
                    // Title3/Regular
                    style =
                        TextStyle(
                            fontSize = 19.sp,
//                        fontFamily = FontFamily(Font(R.font.raleway)),
                            fontWeight = FontWeight(400),
                            color = Color.White,
                        ),
                )
                Spacer(
                    Modifier.height(60.dp),
                )
                Column(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                ) {
                    RoundedButton(
                        onClick = navigateToSelectServer,
                    ) {
                        Text(
                            text = "Sign Up",
                            style = MaterialTheme.typography.labelLarge,
                            color = Color.Black,
                        )
                    }

                    Spacer(
                        Modifier.height(
                            30.dp,
                        ),
                    )
                    RoundedButtonWithOpacity(
                        onClick = navigateToLogin,
                    ) {
                        Text(
                            text = "Log In",
                            style = MaterialTheme.typography.labelLarge,
                            color = Color.White,
                        )
                    }
                    Spacer(
                        Modifier.height(40.dp),
                    )
                }

                Row(
                    Modifier
                        .fillMaxWidth()
                        .padding(50.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    Text(
                        text = "Terms of Service",
                        style =
                            TextStyle(
                                fontSize = 15.sp,
//                            fontFamily = FontFamily(Font(R.font.raleway)),
                                fontWeight = FontWeight(400),
                                color = Color.White,
                            ),
                    )

                    Text(
                        text = "Imprint",
                        style =
                            TextStyle(
                                fontSize = 15.sp,
//                            fontFamily = FontFamily(Font(R.font.raleway)),
                                fontWeight = FontWeight(400),
                                color = Color.White,
                            ),
                    )

                    Text(
                        text = "About",
                        style =
                            TextStyle(
                                fontSize = 15.sp,
//                            fontFamily = FontFamily(Font(R.font.raleway)),
                                fontWeight = FontWeight(400),
                                color = Color.White,
                            ),
                    )
                }
            }
        }

//        color = Color.White,
//                styl
//                modifier = Modifier.padding(16.dp)

//        Column(
//            modifier = Modifier
//                .fillMaxSize()
//                .padding(bottom = 24.dp),
//            horizontalAlignment = Alignment.CenterHorizontally,
//            verticalArrangement = Arrangement.Center,
//        ) {
//            Column(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .wrapContentHeight()
//                    .background(
//                        brush = Brush.verticalGradient(
//                            colors = listOf(
//                                MaterialTheme.colors.surface,
//
//                            )
//                        ),
//                    )
//                    .padding(top = 48.dp),
//                horizontalAlignment = Alignment.CenterHorizontally,
//            ) {
//                appIcon()
//
//                Spacer(Modifier.height(32.dp))
//            }
//
//            Text(
//                text = "Dodo",
//                style = MaterialTheme.typography.h3,
//                textAlign = TextAlign.Center,
//                fontWeight = FontWeight.Bold,
//                modifier = Modifier.padding(horizontal = 24.dp),
//            )
//
//            Spacer(Modifier.height(12.dp))
//
//            Text(
//                text = "Welcome to a completely free and decentralized social media.",
//                style = MaterialTheme.typography.h6,
//                textAlign = TextAlign.Center,
//                fontWeight = FontWeight.SemiBold,
//                modifier = Modifier.padding(horizontal = 24.dp),
//            )
//
//            Spacer(Modifier.height(42.dp))
//
//            MemoryButton(
//                modifier = Modifier
//                    .widthIn(min = 240.dp)
//                    .padding(horizontal = 24.dp),
//                onClick = onGetStartedClicked,
//                text = "Get Started",
//            )
//        }
    }
}

@Suppress("ktlint:standard:function-naming")
@Composable
fun RoundedButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    val buttonColor = Color(0x33FFFFFF) // Adjust the alpha as needed (0.0f to 1.0f)

    Button(
        modifier =
            modifier
                .width(343.dp)
                .height(50.dp)
                .padding(
                    start = 16.dp,
                ).clip(
                    RoundedCornerShape(
                        size = 16.dp,
                    ),
                ),
        onClick = onClick,
        elevation = null,
        colors = ButtonDefaults.buttonColors(containerColor = Color.White),
    ) {
        content()
    }
}

@Suppress("ktlint:standard:function-naming")
@Composable
fun RoundedButtonWithOpacity(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    val buttonColor = Color(0x33FFFFFF) // Adjust the alpha as needed (0.0f to 1.0f)

    Button(
        modifier =
            modifier
                .width(343.dp)
                .height(50.dp)
                .padding(
                    start = 16.dp,
                ).clip(
                    RoundedCornerShape(
                        size = 16.dp,
                    ),
                ),
        onClick = onClick,
        elevation = null,
        colors = ButtonDefaults.buttonColors(containerColor = Color(0x33FFFFFF)),
    ) {
        content()
    }
}

@Suppress("ktlint:standard:function-naming")
@Composable
fun GlowingGradientBackground(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    val colors =
        listOf(
            Color(0XFFFFFFF),
            Color(0XFFFFFFF),
//        Color(0XF6364F6),
//        Color(0XF6364F6),
//        Color(0XF63AFF6),
//        Color(0XF63AFF6),
            Color(0XFF6638F),
            Color(0XF63AFF6),
            Color(0XFC763F6),
            Color(0XF63F6F6),
        )

    var angle by remember { mutableStateOf(0f) }

    val infiniteTransition = rememberInfiniteTransition()

    angle =
        infiniteTransition
            .animateFloat(
                initialValue = 45f,
                targetValue = 620f,
                animationSpec =
                    infiniteRepeatable(
                        animation = tween(durationMillis = 3000, easing = FastOutLinearInEasing),
                        repeatMode = RepeatMode.Reverse,
                    ),
            ).value

    Box(
        modifier =
            modifier
                .background(
                    brush =
                        Brush.linearGradient(
                            colors = colors,
                            start =
                                Offset(
                                    x = angle,
                                    y = 0.toFloat(),
                                ),
                        ),
                ),
    ) {
        // Display the content on top of the background
        content()
    }
}

@Suppress("ktlint:standard:function-naming")
@Composable
fun MemoryMainTextIcon(
    modifier: Modifier =
        Modifier
            .width(273.dp)
            .height(50.dp)
            .padding(start = 16.dp)
            .background(Color.Transparent),
) {
    val imageVector = MyIconPack.Text
    Image(
        imageVector = imageVector,
        colorFilter = ColorFilter.tint(Color.White),
        contentDescription = "Memory main Icon Text",
        modifier = modifier,
    )
}

@Suppress("ktlint:standard:function-naming")
@Composable
fun MemoryTextIcon(modifier: Modifier = Modifier) {
    Image(
        imageVector = MyIconPack.Memory,
        contentDescription = "Memory Icon Text",
        colorFilter = ColorFilter.tint(color = Color(0xFF000000)),
        modifier = modifier,
    )
}

@Suppress("ktlint:standard:function-naming")
// @Preview
@Composable
private fun PreviewLandingContent() {
    MemoryTheme(true) {
        LandingContent(
            navigateToSelectServer = {},
            navigateToLogin = {},
        )
    }
}
