package com.amoretech.memory.landing.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

/**
 * Landing screen for signed-out users
 */
@Composable
@Suppress("ktlint:standard:function-naming")
fun LandingScreen(
    modifier: Modifier = Modifier,
    onNavigateToSelectServer: () -> Unit,
    onNavigateToSignIn: () -> Unit,
) {
    val scrollState = rememberScrollState()

    Column(
        modifier =
            modifier
                .fillMaxSize()
                .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        LandingContent(
            navigateToSelectServer = onNavigateToSelectServer,
            navigateToLogin = onNavigateToSignIn,
        )
    }
}
