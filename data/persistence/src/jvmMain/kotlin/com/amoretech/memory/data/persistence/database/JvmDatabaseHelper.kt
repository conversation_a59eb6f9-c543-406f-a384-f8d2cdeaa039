package com.amoretech.memory.data.persistence.database

import androidx.room.Room
import androidx.room.RoomDatabase
import com.amoretech.memory.data.persistence.di.AUTH_DB_NAME
import com.amoretech.memory.data.persistence.di.FEED_DB_NAME
import com.amoretech.memory.data.persistence.di.MASTODON_DB_NAME
import java.io.File

fun getAuthenticationDatabaseBuilder(): RoomDatabase.Builder<AuthenticationDatabase> {
    val dbFile = File(System.getProperty("java.io.tmpdir"), AUTH_DB_NAME)
    return Room.databaseBuilder<AuthenticationDatabase>(
        name = dbFile.absolutePath,
    )
}

fun getTimelineDatabaseBuilder(): RoomDatabase.Builder<TimelineDatabase> {
    val dbFile = File(System.getProperty("java.io.tmpdir"), FEED_DB_NAME)
    return Room.databaseBuilder<TimelineDatabase>(
        name = dbFile.absolutePath,
    )
}

fun getMastodonDatabaseBuilder(): RoomDatabase.Builder<MastodonDatabase> {
    val dbFile = File(System.getProperty("java.io.tmpdir"), MASTODON_DB_NAME)
    return Room.databaseBuilder<MastodonDatabase>(
        name = dbFile.absolutePath,
    )
}
