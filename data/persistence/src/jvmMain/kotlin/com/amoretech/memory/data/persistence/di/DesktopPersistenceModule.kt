package com.amoretech.memory.data.persistence.di

import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import com.amoretech.memory.data.persistence.database.AuthenticationDatabase
import com.amoretech.memory.data.persistence.database.MastodonDatabase
import com.amoretech.memory.data.persistence.database.TimelineDatabase
import com.amoretech.memory.data.persistence.database.getAuthenticationDatabaseBuilder
import com.amoretech.memory.data.persistence.database.getMastodonDatabaseBuilder
import com.amoretech.memory.data.persistence.database.getTimelineDatabaseBuilder
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorage
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorageImpl
import com.russhwolf.settings.PreferencesSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.serialization.json.Json
import org.koin.core.module.Module
import org.koin.dsl.module

private const val APP_DATA_DIR_NAME = "dodo"

/**
 * Koin DI module for all JVM specific persistence dependencies.
 */
actual val persistenceModule: Module =
    module {
        println("Using JVM persistenceModule")
        single<DodoAuthStorage> {
            DodoAuthStorageImpl(
                settings =
                    PreferencesSettings
                        .Factory() // Use PreferencesSettings.Factory for JVM
                        .create(AUTH_SETTINGS_NAME),
                json =
                    Json {
                        ignoreUnknownKeys = true
                        encodeDefaults = false
                    },
            )
        }

//        // Function to get the application data directory in the user's home
//        fun getAppDataDir(): File {
//            val userHome = System.getProperty("user.home")
//            val appDataDir = File(userHome, APP_DATA_DIR_NAME)
//            if (!appDataDir.exists()) {
//                appDataDir.mkdirs() // Create directory if it doesn't exist
//            }
//            return appDataDir
//        }
//
//        // Provide the AuthenticationDatabase instance for JVM
//        single<AuthenticationDatabase> {
//            val dbFile = File(getAppDataDir(), AUTH_DB_NAME)
//            Room
//                .databaseBuilder<AuthenticationDatabase>(
//                    name = dbFile.absolutePath, // Use the absolute path to the database file
//                )
//                // Add migrations here if needed
//                // .addMigrations(...)
//                .fallbackToDestructiveMigration(false) // Configure migration strategy
//                .build()
//        }

        single<AuthenticationDatabase> {
            getAuthenticationDatabaseBuilder()
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }

        // Provide the Authentication DAO
        single { get<AuthenticationDatabase>().applicationDao() }

//        // Provide the TimelineDatabase instance for JVM
//        single<TimelineDatabase> {
//            val dbFile = File(getAppDataDir(), FEED_DB_NAME)
//            Room
//                .databaseBuilder<TimelineDatabase>(
//                    name = dbFile.absolutePath, // Use the absolute path to the database file
//                )
//                // Add migrations here if needed
//                // .addMigrations(...)
//                .fallbackToDestructiveMigration(false) // Configure migration strategy
//                .build()
//        }

        single<TimelineDatabase> {
            getTimelineDatabaseBuilder()
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }

        // Provide the Timeline DAOs
        single { get<TimelineDatabase>().failedWriteDao() }
        single { get<TimelineDatabase>().statusDao() }

        single<MastodonDatabase> {
            getMastodonDatabaseBuilder()
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }

        // Provide the Timeline DAOs
        single { get<MastodonDatabase>().mastodonDao() }
    }
