package com.amoretech.memory.data.persistence

// import com.amoretech.memory.data.persistence.entity.ApplicationEntity
// import kotlinx.coroutines.test.runTest
// import kotlin.test.Test
// import kotlin.test.assertEquals
// import kotlin.test.assertNotNull
//
// internal class AuthenticationDatabaseTests {
//
//    @Test
//    fun `insert application into clean database should contain one entry`() = runTest {
//        // given
//        val database = provideTestAuthenticationDatabase()
//        val dao = database.applicationDao()
// //        val driver =
// //            provideTestSqlDriverForAuthentication()
// //        val database = AuthenticationDatabase(driver = driver)
//
//        // when
//        dao.deleteAllApplications()
//        val application = ApplicationEntity(
//            instance = "androiddev.social",
//            clientId = "test_client_id",
//            clientSecret = "test_client_secret",
//            redirectUri = "redirect_uri"
//        )
//        dao.insertApplication(application)
// //        database.applicationQueries.deleteAll()
// //        val application = Application(
// //            instance = "androiddev.social",
// //            client_id = "test_client_id",
// //            client_secret = "test_client_secret",
// //            redirect_uri = "redirect_uri"
// //        )
// //        database.applicationQueries.insertApplication(application)
//
//        // then
//        val result = dao.selectByServer("androiddev.social")
//
//        assertNotNull(result)
//        assertEquals(application.clientId, result.clientId)
//        assertEquals(application.clientSecret, result.clientSecret)
//        assertEquals(application.redirectUri, result.redirectUri)
// //        val result = database
// //            .applicationQueries
// //            .selectByServer("androiddev.social")
// //            .executeAsOneOrNull()
// //
// //        assertNotNull(result)
// //        assertEquals(application.client_id, result.client_id)
// //        assertEquals(application.client_secret, result.client_secret)
// //        assertEquals(application.redirect_uri, result.redirect_uri)
//    }
// }
