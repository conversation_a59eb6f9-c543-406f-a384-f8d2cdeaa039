package com.amoretech.memory.data.persistence

// import androidx.room.Room
// import com.amoretech.memory.data.persistence.database.AuthenticationDatabase

// actual fun provideTestSqlDriverForAuthentication(): SqlDriver {
//    return NativeSqliteDriver(
//        DatabaseConfiguration(
//            name = "authentication.db",
//            version = AuthenticationDatabase.Schema.version.toInt(),
//            create = { connection ->
//                wrapConnection(connection) { AuthenticationDatabase.Schema.create(it) }
//            },
//            upgrade = { connection, oldVersion, newVersion ->
//                wrapConnection(connection) {
//                    AuthenticationDatabase.Schema.migrate(
//                        it, oldVersion.toLong(), newVersion.toLong()
//                    )
//                }
//            },
//            inMemory = true
//        )
//    )
// }

// actual fun provideTestAuthenticationDatabase(): AuthenticationDatabase {
//    return Room.inMemoryDatabaseBuilder<AuthenticationDatabase>().build()
// }
