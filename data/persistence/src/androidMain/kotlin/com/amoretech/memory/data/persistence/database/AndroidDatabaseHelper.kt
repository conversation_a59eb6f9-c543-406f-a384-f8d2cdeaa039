package com.amoretech.memory.data.persistence.database

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabase
import com.amoretech.memory.data.persistence.di.AUTH_DB_NAME
import com.amoretech.memory.data.persistence.di.FEED_DB_NAME
import com.amoretech.memory.data.persistence.di.MASTODON_DB_NAME

fun getAuthenticationDatabaseBuilder(ctx: Context): RoomDatabase.Builder<AuthenticationDatabase> {
    val appContext = ctx.applicationContext
    val dbFile = appContext.getDatabasePath(AUTH_DB_NAME)
    return Room.databaseBuilder<AuthenticationDatabase>(
        context = appContext,
        name = dbFile.absolutePath,
    )
}

fun getTimelineDatabaseBuilder(ctx: Context): RoomDatabase.Builder<TimelineDatabase> {
    val appContext = ctx.applicationContext
    val dbFile = appContext.getDatabasePath(FEED_DB_NAME)
    return Room.databaseBuilder<TimelineDatabase>(
        context = appContext,
        name = dbFile.absolutePath,
    )
}

fun getMastodonDatabaseBuilder(ctx: Context): RoomDatabase.Builder<MastodonDatabase> {
    val appContext = ctx.applicationContext
    val dbFile = appContext.getDatabasePath(MASTODON_DB_NAME)
    return Room.databaseBuilder<MastodonDatabase>(
        context = appContext,
        name = dbFile.absolutePath,
    )
}
