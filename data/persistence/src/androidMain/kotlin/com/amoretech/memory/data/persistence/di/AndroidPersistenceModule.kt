package com.amoretech.memory.data.persistence.di

import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import com.amoretech.memory.data.persistence.database.AuthenticationDatabase
import com.amoretech.memory.data.persistence.database.MastodonDatabase
import com.amoretech.memory.data.persistence.database.TimelineDatabase
import com.amoretech.memory.data.persistence.database.getAuthenticationDatabaseBuilder
import com.amoretech.memory.data.persistence.database.getMastodonDatabaseBuilder
import com.amoretech.memory.data.persistence.database.getTimelineDatabaseBuilder
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorage
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorageImpl
import com.russhwolf.settings.SharedPreferencesSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.Json
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin DI module for all android specific persistence dependencies
 */
actual val persistenceModule: Module =
    module {
        single<DodoAuthStorage> {
            DodoAuthStorageImpl(
                settings =
                    SharedPreferencesSettings
                        .Factory(
                            context = get(),
                        ).create(AUTH_SETTINGS_NAME),
                json =
                    Json {
                        ignoreUnknownKeys = true
                        encodeDefaults = false
                    },
            )
        }

        // AuthenticationDatabase
//        single<AuthenticationDatabase> {
//            Room
//                .databaseBuilder(
//                    androidContext(),
//                    AuthenticationDatabase::class.java,
//                    AUTH_DB_NAME,
//                ).fallbackToDestructiveMigration(false)
//                .build()
//        }
        single<AuthenticationDatabase> {
            getAuthenticationDatabaseBuilder(get())
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
        single { get<AuthenticationDatabase>().applicationDao() }

        // TimelineDatabase
//        single<TimelineDatabase> {
//            Room
//                .databaseBuilder(
//                    androidContext(),
//                    TimelineDatabase::class.java,
//                    FEED_DB_NAME,
//                ).fallbackToDestructiveMigration(false)
//                .build()
//        }
        single<TimelineDatabase> {
            getTimelineDatabaseBuilder(get())
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
        single { get<TimelineDatabase>().failedWriteDao() }
        single { get<TimelineDatabase>().statusDao() }

        single<MastodonDatabase> {
            getMastodonDatabaseBuilder(get())
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
        single { get<MastodonDatabase>().mastodonDao() }
    }
