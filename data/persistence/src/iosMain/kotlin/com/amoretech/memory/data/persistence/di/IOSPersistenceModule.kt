package com.amoretech.memory.data.persistence.di

import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import com.amoretech.memory.data.persistence.database.AuthenticationDatabase
import com.amoretech.memory.data.persistence.database.MastodonDatabase
import com.amoretech.memory.data.persistence.database.TimelineDatabase
import com.amoretech.memory.data.persistence.database.getAuthenticationDatabaseBuilder
import com.amoretech.memory.data.persistence.database.getMastodonDatabaseBuilder
import com.amoretech.memory.data.persistence.database.getTimelineDatabaseBuilder
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorage
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorageImpl
import com.russhwolf.settings.NSUserDefaultsSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.serialization.json.Json
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin DI module for all iOS specific persistence dependencies
 */
actual val persistenceModule: Module =
    module {
        single<DodoAuthStorage> {
            DodoAuthStorageImpl(
                settings = NSUserDefaultsSettings.Factory().create(AUTH_SETTINGS_NAME),
                json =
                    Json {
                        ignoreUnknownKeys = true
                        encodeDefaults = false
                    },
            )
        }

//        single<AuthenticationDatabase> {
//            getDatabaseBuilder().build()
//        }
        // Authentication database and dao
        single<AuthenticationDatabase> {
            getAuthenticationDatabaseBuilder()
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
        single { get<AuthenticationDatabase>().applicationDao() }

        // Timeline database and dao
        single<TimelineDatabase> {
            getTimelineDatabaseBuilder()
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
        single { get<TimelineDatabase>().failedWriteDao() }
        single { get<TimelineDatabase>().statusDao() }

        // Application database and dao
        single<MastodonDatabase> {
            getMastodonDatabaseBuilder()
                .fallbackToDestructiveMigration(false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
        single { get<MastodonDatabase>().mastodonDao() }
    }
