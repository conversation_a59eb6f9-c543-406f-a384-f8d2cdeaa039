package com.amoretech.memory.data.persistence.di

import androidx.room.Room
import androidx.room.RoomDatabase
import com.amoretech.memory.data.persistence.database.AuthenticationDatabase
import kotlinx.cinterop.ExperimentalForeignApi
import platform.Foundation.NSDocumentDirectory
import platform.Foundation.NSFileManager
import platform.Foundation.NSUserDomainMask

fun getDatabaseBuilder(): RoomDatabase.Builder<AuthenticationDatabase> {
    val dbFilePath = documentDirectory() + "/" + AUTH_DB_NAME
    return Room.databaseBuilder<AuthenticationDatabase>(
        name = dbFilePath,
    )
}

@OptIn(ExperimentalForeignApi::class)
private fun documentDirectory(): String {
    val documentDirectory =
        NSFileManager.defaultManager.URLForDirectory(
            directory = NSDocumentDirectory,
            inDomain = NSUserDomainMask,
            appropriateForURL = null,
            create = false,
            error = null,
        )
    return requireNotNull(documentDirectory?.path)
}
