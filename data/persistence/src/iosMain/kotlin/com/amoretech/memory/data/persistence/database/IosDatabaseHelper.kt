package com.amoretech.memory.data.persistence.database

import androidx.room.Room
import androidx.room.RoomDatabase
import com.amoretech.memory.data.persistence.di.AUTH_DB_NAME
import com.amoretech.memory.data.persistence.di.FEED_DB_NAME
import com.amoretech.memory.data.persistence.di.MASTODON_DB_NAME
import kotlinx.cinterop.ExperimentalForeignApi
import platform.Foundation.NSDocumentDirectory
import platform.Foundation.NSFileManager
import platform.Foundation.NSUserDomainMask

fun getAuthenticationDatabaseBuilder(): RoomDatabase.Builder<AuthenticationDatabase> {
    val dbFilePath = documentDirectory() + "/" + AUTH_DB_NAME
    return Room.databaseBuilder<AuthenticationDatabase>(
        name = dbFilePath,
    )
}

fun getTimelineDatabaseBuilder(): RoomDatabase.Builder<TimelineDatabase> {
    val dbFilePath = documentDirectory() + "/" + FEED_DB_NAME
    return Room.databaseBuilder<TimelineDatabase>(
        name = dbFilePath,
    )
}

fun getMastodonDatabaseBuilder(): RoomDatabase.Builder<MastodonDatabase> {
    val dbFilePath = documentDirectory() + "/" + MASTODON_DB_NAME
    return Room.databaseBuilder<MastodonDatabase>(
        name = dbFilePath,
    )
}

@OptIn(ExperimentalForeignApi::class)
private fun documentDirectory(): String {
    val documentDirectory =
        NSFileManager.defaultManager.URLForDirectory(
            directory = NSDocumentDirectory,
            inDomain = NSUserDomainMask,
            appropriateForURL = null,
            create = false,
            error = null,
        )
    return requireNotNull(documentDirectory?.path)
}
