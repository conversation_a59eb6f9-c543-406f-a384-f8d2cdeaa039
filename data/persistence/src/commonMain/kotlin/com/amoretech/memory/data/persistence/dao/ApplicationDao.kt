package com.amoretech.memory.data.persistence.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.amoretech.memory.data.persistence.entity.ApplicationEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ApplicationDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertApplication(application: ApplicationEntity)

    @Query("SELECT * FROM Application")
    fun selectAllApplications(): Flow<List<ApplicationEntity>>

    @Query("DELETE FROM Application")
    suspend fun deleteAllApplications()

    @Query("SELECT * FROM Application WHERE instance = :instance")
    suspend fun selectByServer(instance: String): ApplicationEntity?
}
