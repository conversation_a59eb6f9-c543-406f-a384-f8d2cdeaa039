package com.amoretech.memory.data.persistence.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.amoretech.memory.data.persistence.entity.StatusEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface StatusDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFeedItem(status: StatusEntity)

    @Query("SELECT * FROM StatusDB WHERE type = 'HOME' ORDER BY createdAt DESC")
    fun selectHomeItems(): Flow<List<StatusEntity>>

    @Query("DELETE FROM StatusDB")
    suspend fun deleteAllStatusDB()
}
