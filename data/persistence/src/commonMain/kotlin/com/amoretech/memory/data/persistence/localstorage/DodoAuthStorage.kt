package com.amoretech.memory.data.persistence.localstorage

import kotlinx.coroutines.flow.Flow

/**
 * Contract for key => value storage for any authentication related data
 */
interface DodoAuthStorage {
    /**
     * The current domain/server the user is logged into.
     * This is used to query the right account info since
     * the user can have multiple accounts
     */
    var currentDomain: String?

    /**
     * List of servers that user has access to
     */
    val authorizedServersFlow: Flow<List<String>>

    /**
     * Save the @param token keyed by @param server
     */
    suspend fun saveAccessToken(
        server: String,
        token: String,
    )

    /**
     * Get the Access token for @param server
     */
    fun getAccessToken(server: String): String?

    /**
     * Remove stored access token for selected @param server
     */
    fun clearAccessToken(server: String)
}
