package com.amoretech.memory.data.persistence.setting

import kotlinx.coroutines.flow.Flow

/**
 * Enhanced AppSetting interface for comprehensive user state management
 * Supports multiple user states: unregistered, registered (pending OOB), verified, and authenticated
 */
interface AppSetting {
    // Authentication state
    val isLoggedIn: Boolean
    var serverName: String?

    // User registration state
    val userState: UserState
    val userStateFlow: Flow<UserState>

    // OOB verification state
    var pendingOobVerification: Boolean
    var oobCode: String?
    val oobCodeFlow: Flow<String?>

    // User account information
    var userEmail: String?
    var userName: String?

    // Token management
    fun saveAccessToken(token: String)
    fun clearAuthData()

    // User state management
    fun setUserRegistered(email: String, userName: String, domain: String)
    fun setOobCodeReceived(oobCode: String)
    fun setUserVerified()
    fun clearUserData()

    // Flow observables
    val serverNameFlow: Flow<String?>
    val userEmailFlow: Flow<String?>
    val userNameFlow: Flow<String?>
}

/**
 * Represents the current state of user registration and verification
 */
enum class UserState {
    /** User has never registered or has been logged out */
    UNREGISTERED,

    /** User has registered but hasn't entered OOB code yet */
    REGISTERED_PENDING_OOB,

    /** User has entered OOB code and is verified but not fully authenticated */
    VERIFIED_PENDING_AUTH,

    /** User is fully authenticated and can access the app */
    AUTHENTICATED
}
