package com.amoretech.memory.data.persistence.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Application entity
 *
 * @property instance
 * @property clientId
 * @property clientSecret
 * @property redirectUri
 * @constructor Create empty Application entity
 */
@Entity(tableName = "Application")
data class ApplicationEntity(
    @PrimaryKey(autoGenerate = false)
    val instance: String,
    val clientId: String,
    val clientSecret: String,
    val redirectUri: String,
)
