package com.amoretech.memory.data.persistence.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.amoretech.memory.data.persistence.entity.AppTokenEntity
import com.amoretech.memory.data.persistence.entity.InstanceRegistrationEntity

@Dao
interface MastodonDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveInstanceRegistrationEntity(registration: InstanceRegistrationEntity)

    @Query("SELECT * FROM instance_registrations WHERE domain = :domain")
    suspend fun getInstanceRegistrationEntity(domain: String): InstanceRegistrationEntity?

    // App tokens
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveAppTokenEntity(token: AppTokenEntity)

    @Query("SELECT * FROM app_tokens WHERE domain = :domain")
    suspend fun getAppTokenEntity(domain: String): AppTokenEntity?
}
