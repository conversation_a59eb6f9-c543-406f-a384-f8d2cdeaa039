package com.amoretech.memory.data.persistence.di

import org.koin.core.module.Module

/**
 * Expect a platform specific Koin module containing all
 * bean definitions for persistent storage related classes
 */
@Suppress("NO_ACTUAL_FOR_EXPECT")
expect val persistenceModule: Module

internal const val AUTH_DB_NAME = "authentication.db"
internal const val FEED_DB_NAME = "feed.db"
internal const val AUTH_SETTINGS_NAME = "DodoAuthSettings"
internal const val MASTODON_DB_NAME = "mastodon.db"
