package com.amoretech.memory.data.persistence.database

import androidx.room.ConstructedBy
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.RoomDatabaseConstructor
import com.amoretech.memory.data.persistence.dao.ApplicationDao
import com.amoretech.memory.data.persistence.entity.ApplicationEntity

@Database(
    entities = [
        ApplicationEntity::class,
    ],
    version = 1,
    exportSchema = true,
)
@ConstructedBy(AuthenticationDatabaseConstructor::class)
abstract class AuthenticationDatabase : RoomDatabase() {
    abstract fun applicationDao(): ApplicationDao
}

// The Room compiler generates the `actual` implementations.
@Suppress("NO_ACTUAL_FOR_EXPECT", "EXPECT_ACTUAL_CLASSIFIERS_ARE_IN_BETA_WARNING")
expect object AuthenticationDatabaseConstructor : RoomDatabaseConstructor<AuthenticationDatabase> {
    override fun initialize(): AuthenticationDatabase
}
