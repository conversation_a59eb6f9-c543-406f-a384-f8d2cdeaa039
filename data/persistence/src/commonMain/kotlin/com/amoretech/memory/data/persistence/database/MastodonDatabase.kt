package com.amoretech.memory.data.persistence.database

import androidx.room.ConstructedBy
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.RoomDatabaseConstructor
import androidx.room.TypeConverters
import com.amoretech.memory.data.persistence.converter.Converters
import com.amoretech.memory.data.persistence.dao.MastodonDao
import com.amoretech.memory.data.persistence.entity.AppTokenEntity
import com.amoretech.memory.data.persistence.entity.InstanceRegistrationEntity

@Database(
    entities = [InstanceRegistrationEntity::class, AppTokenEntity::class],
    version = 1,
    exportSchema = true,
)
@TypeConverters(Converters::class)
@ConstructedBy(MastodonDatabaseConstructor::class)
abstract class MastodonDatabase : RoomDatabase() {
    abstract fun mastodonDao(): MastodonDao
}

// The Room compiler generates the `actual` implementations.
@Suppress("NO_ACTUAL_FOR_EXPECT", "EXPECT_ACTUAL_CLASSIFIERS_ARE_IN_BETA_WARNING")
expect object MastodonDatabaseConstructor : RoomDatabaseConstructor<MastodonDatabase> {
    override fun initialize(): MastodonDatabase
}
