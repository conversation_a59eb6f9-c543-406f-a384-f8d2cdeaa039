package com.amoretech.memory.data.persistence.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.amoretech.memory.data.persistence.constant.LocalDatabaseConstant

@Entity(tableName = LocalDatabaseConstant.TABLE_APP_TOKEN)
data class AppTokenEntity(
    @PrimaryKey(autoGenerate = false)
    val domain: String,
    val accessToken: String,
    val tokenType: String,
    val scope: String,
    val createdAt: Long,
)
