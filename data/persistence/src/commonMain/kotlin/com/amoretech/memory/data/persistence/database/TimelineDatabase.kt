package com.amoretech.memory.data.persistence.database

import androidx.room.ConstructedBy
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.RoomDatabaseConstructor
import androidx.room.TypeConverters
import com.amoretech.memory.data.persistence.converter.Converters
import com.amoretech.memory.data.persistence.dao.FailedWriteDao
import com.amoretech.memory.data.persistence.dao.StatusDao
import com.amoretech.memory.data.persistence.entity.FailedWriteEntity
import com.amoretech.memory.data.persistence.entity.StatusEntity

@Database(
    entities = [FailedWriteEntity::class, StatusEntity::class],
    version = 1,
    exportSchema = true,
)
@TypeConverters(Converters::class)
@ConstructedBy(TimelineDatabaseConstructor::class)
abstract class TimelineDatabase : RoomDatabase() {
    abstract fun failedWriteDao(): FailedWriteDao

    abstract fun statusDao(): StatusDao
}

// The Room compiler generates the `actual` implementations.
@Suppress("NO_ACTUAL_FOR_EXPECT", "EXPECT_ACTUAL_CLASSIFIERS_ARE_IN_BETA_WARNING")
expect object TimelineDatabaseConstructor : RoomDatabaseConstructor<TimelineDatabase> {
    override fun initialize(): TimelineDatabase
}
