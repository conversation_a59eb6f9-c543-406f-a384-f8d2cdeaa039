package com.amoretech.memory.data.persistence.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy.Companion.REPLACE
import androidx.room.Query
import com.amoretech.memory.data.persistence.entity.FailedWriteEntity

@Dao
interface FailedWriteDao {
    @Query("SELECT * FROM failedWrite WHERE key = :key")
    suspend fun getFailedWrite(key: String): FailedWriteEntity?

    @Insert(onConflict = REPLACE)
    suspend fun upsertFailedWrite(failedWrite: FailedWriteEntity)

    @Query("DELETE FROM failedWrite WHERE key = :key")
    suspend fun deleteFailedWrite(key: String)

    @Query("DELETE FROM failedWrite")
    suspend fun deleteAllFailedWrites()
}
