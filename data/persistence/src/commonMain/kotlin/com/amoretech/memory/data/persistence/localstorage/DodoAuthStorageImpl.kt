package com.amoretech.memory.data.persistence.localstorage

import com.russhwolf.settings.ExperimentalSettingsApi
import com.russhwolf.settings.ObservableSettings
import com.russhwolf.settings.coroutines.getStringOrNullFlow
import com.russhwolf.settings.nullableString
import kotlinx.atomicfu.locks.ReentrantLock
import kotlinx.atomicfu.locks.reentrantLock
import kotlinx.atomicfu.locks.withLock
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json
import kotlin.text.get

@OptIn(ExperimentalSettingsApi::class)
internal class DodoAuthStorageImpl(
    private val settings: ObservableSettings,
    private val json: Json,
    private val lock: ReentrantLock = reentrantLock(),
) : DodoAuthStorage {
    private companion object {
        private const val KEY_DOMAIN_CACHE = "key_domain_cache"
        private const val KEY_ACCESS_TOKENS_CACHE = "key_access_tokens_cache"
    }

    override var currentDomain: String?
        get() = settings.nullableString(KEY_DOMAIN_CACHE).toString()
        set(value) {
            if (value != null) {
                settings.putString(KEY_DOMAIN_CACHE, value)
            } else {
                settings.remove(KEY_DOMAIN_CACHE)
            }
        }

    override val authorizedServersFlow: Flow<List<String>> =
        flow {
            settings.getStringOrNullFlow(KEY_ACCESS_TOKENS_CACHE).collect { authTokens ->
                if (authTokens == null) {
                    emit(listOf())
                } else {
                    val serverList =
                        json
                            .decodeFromString(ListSerializer(AccessToken.serializer()), authTokens)
                            .associateBy { it.server }
                            .keys
                            .toList()
                    emit(serverList)
                }
            }
        }

    /**
     * The user can set up multiple accounts on their device. So we
     * key the AccessToken by the unique server/domain
     */
    private var diskCache: LinkedHashMap<String, AccessToken> =
        run {
            settings
                .getStringOrNull(KEY_ACCESS_TOKENS_CACHE)
                ?.let { str ->
                    json
                        .decodeFromString(ListSerializer(AccessToken.serializer()), str)
                        .associateByTo(linkedMapOf()) { it.server }
                }
                ?: linkedMapOf()
        }

    // To initialize memCache with the loaded diskCache
    private val memCache: LinkedHashMap<String, AccessToken> = LinkedHashMap(diskCache)

    override fun getAccessToken(server: String): String? = lock.withLock { memCache[server]?.token }

    override suspend fun saveAccessToken(
        server: String,
        token: String,
    ) {
        lock.withLock {
            memCache[server] = AccessToken(token = token, server = server)
            diskCache = memCache
        }
    }

    override fun clearAccessToken(server: String) {
        lock.withLock {
            memCache.remove(server)
            diskCache = memCache
        }
    }

    @Serializable
    private data class AccessToken(
        val token: String,
        val server: String,
    )
}
