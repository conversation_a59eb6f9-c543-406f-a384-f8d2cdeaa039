package com.amoretech.memory.data.persistence.setting

import com.russhwolf.settings.ExperimentalSettingsApi
import com.russhwolf.settings.ObservableSettings
import com.russhwolf.settings.coroutines.getBooleanFlow
import com.russhwolf.settings.coroutines.getStringOrNullFlow
import com.russhwolf.settings.nullableString
import kotlinx.atomicfu.locks.ReentrantLock
import kotlinx.atomicfu.locks.reentrantLock
import kotlinx.atomicfu.locks.withLock
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map

@OptIn(ExperimentalSettingsApi::class)
internal class AppSettingImpl(
    private val settings: ObservableSettings,
    private val lock: ReentrantLock = reentrantLock(),
) : AppSetting {
    private companion object {
        private const val KEY_SERVER_NAME = "key_server_name"
        private const val KEY_ACCESS_TOKEN = "key_access_token"
        private const val KEY_USER_STATE = "key_user_state"
        private const val KEY_PENDING_OOB = "key_pending_oob"
        private const val KEY_OOB_CODE = "key_oob_code"
        private const val KEY_USER_EMAIL = "key_user_email"
        private const val KEY_USER_NAME = "key_user_name"
    }

    // to initialize with default values
    init {
        if (settings.nullableString(KEY_ACCESS_TOKEN) == null) {
            clearAuthData()
        }
    }

    override val isLoggedIn: Boolean
        get() = lock.withLock { settings.nullableString(KEY_ACCESS_TOKEN) != null }

    private var _serverName: String? by settings.nullableString(KEY_SERVER_NAME)
    private var _userEmail: String? by settings.nullableString(KEY_USER_EMAIL)
    private var _userName: String? by settings.nullableString(KEY_USER_NAME)
    private var _oobCode: String? by settings.nullableString(KEY_OOB_CODE)

    override var serverName: String?
        get() = lock.withLock { _serverName }
        set(value) {
            lock.withLock {
                _serverName = value
            }
        }

    override var userEmail: String?
        get() = lock.withLock { _userEmail }
        set(value) {
            lock.withLock {
                _userEmail = value
            }
        }

    override var userName: String?
        get() = lock.withLock { _userName }
        set(value) {
            lock.withLock {
                _userName = value
            }
        }

    override var oobCode: String?
        get() = lock.withLock { _oobCode }
        set(value) {
            lock.withLock {
                _oobCode = value
            }
        }

    override val userState: UserState
        get() = lock.withLock {
            val stateString = settings.getStringOrNull(KEY_USER_STATE)
            try {
                stateString?.let { UserState.valueOf(it) } ?: UserState.UNREGISTERED
            } catch (e: IllegalArgumentException) {
                UserState.UNREGISTERED
            }
        }

    override var pendingOobVerification: Boolean
        get() = lock.withLock { settings.getBoolean(KEY_PENDING_OOB, false) }
        set(value) {
            lock.withLock {
                settings.putBoolean(KEY_PENDING_OOB, value)
            }
        }

    override fun saveAccessToken(token: String) {
        lock.withLock {
            settings.putString(KEY_ACCESS_TOKEN, token)
            // Update user state to authenticated when access token is saved
            settings.putString(KEY_USER_STATE, UserState.AUTHENTICATED.name)
        }
    }

    override fun clearAuthData() {
        lock.withLock {
            settings.remove(KEY_ACCESS_TOKEN)
            settings.remove(KEY_SERVER_NAME)
            // Reset user state to unregistered when clearing auth data
            settings.putString(KEY_USER_STATE, UserState.UNREGISTERED.name)
            settings.remove(KEY_PENDING_OOB)
            settings.remove(KEY_OOB_CODE)
            settings.remove(KEY_USER_EMAIL)
            settings.remove(KEY_USER_NAME)
        }
    }

    override fun setUserRegistered(email: String, userName: String, domain: String) {
        lock.withLock {
            _userEmail = email
            _userName = userName
            _serverName = domain
            pendingOobVerification = true
            settings.putString(KEY_USER_STATE, UserState.REGISTERED_PENDING_OOB.name)
        }
    }

    override fun setOobCodeReceived(oobCode: String) {
        lock.withLock {
            _oobCode = oobCode
            pendingOobVerification = false
            settings.putString(KEY_USER_STATE, UserState.VERIFIED_PENDING_AUTH.name)
        }
    }

    override fun setUserVerified() {
        lock.withLock {
            settings.putString(KEY_USER_STATE, UserState.VERIFIED_PENDING_AUTH.name)
        }
    }

    override fun clearUserData() {
        lock.withLock {
            settings.remove(KEY_USER_EMAIL)
            settings.remove(KEY_USER_NAME)
            settings.remove(KEY_OOB_CODE)
            settings.remove(KEY_PENDING_OOB)
            settings.putString(KEY_USER_STATE, UserState.UNREGISTERED.name)
        }
    }

    // Flow observables
    override val serverNameFlow: Flow<String?> =
        settings.getStringOrNullFlow(KEY_SERVER_NAME)

    override val userEmailFlow: Flow<String?> =
        settings.getStringOrNullFlow(KEY_USER_EMAIL)

    override val userNameFlow: Flow<String?> =
        settings.getStringOrNullFlow(KEY_USER_NAME)

    override val oobCodeFlow: Flow<String?> =
        settings.getStringOrNullFlow(KEY_OOB_CODE)

    override val userStateFlow: Flow<UserState> =
        settings.getStringOrNullFlow(KEY_USER_STATE).map { stateString ->
            try {
                stateString?.let { UserState.valueOf(it) } ?: UserState.UNREGISTERED
            } catch (e: IllegalArgumentException) {
                UserState.UNREGISTERED
            }
        }
}
