package com.amoretech.memory.data.persistence.setting

import com.russhwolf.settings.ExperimentalSettingsApi
import com.russhwolf.settings.ObservableSettings
import com.russhwolf.settings.coroutines.getBooleanFlow
import com.russhwolf.settings.coroutines.getStringOrNullFlow
import com.russhwolf.settings.nullableString
import kotlinx.atomicfu.locks.ReentrantLock
import kotlinx.atomicfu.locks.reentrantLock
import kotlinx.atomicfu.locks.withLock
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map

@OptIn(ExperimentalSettingsApi::class)
internal class AppSettingImpl(
    private val settings: ObservableSettings,
    private val lock: ReentrantLock = reentrantLock(),
) : AppSetting {
    private companion object {
        private const val KEY_SERVER_NAME = "key_server_name"
        private const val KEY_ACCESS_TOKEN = "key_access_token"
        private const val KEY_USER_STATE = "key_user_state"
        private const val KEY_PENDING_OOB = "key_pending_oob"
        private const val KEY_OOB_CODE = "key_oob_code"
        private const val KEY_USER_EMAIL = "key_user_email"
        private const val KEY_USER_NAME = "key_user_name"
    }

    // to initialize with default values
    init {
        if (settings.nullableString(KEY_ACCESS_TOKEN) == null) {
            clearAuthData()
        }
    }

    override val isLoggedIn: Boolean
        get() = lock.withLock { settings.nullableString(KEY_ACCESS_TOKEN) != null }

    private var _serverName: String? by settings.nullableString(KEY_SERVER_NAME)

    override var serverName: String?
        get() = lock.withLock { _serverName }
        set(value) {
            lock.withLock {
                _serverName = value
            }
        }

    override fun saveAccessToken(token: String) {
        lock.withLock {
            settings.putString(KEY_ACCESS_TOKEN, token)
        }
    }

    override fun clearAuthData() {
        lock.withLock {
            settings.remove(KEY_ACCESS_TOKEN)
            settings.remove(KEY_SERVER_NAME)
        }
    }

    override val serverNameFlow: Flow<String?> =
        flow {
            emit(lock.withLock { serverName })
        }
}
