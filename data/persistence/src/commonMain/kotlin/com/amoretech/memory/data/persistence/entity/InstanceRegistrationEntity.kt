package com.amoretech.memory.data.persistence.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.amoretech.memory.data.persistence.constant.LocalDatabaseConstant

@Entity(tableName = LocalDatabaseConstant.TABLE_INSTANCE_REGISTRATION)
data class InstanceRegistrationEntity(
    @PrimaryKey(autoGenerate = false)
    val domain: String,
    val clientId: String,
    val clientSecret: String,
)
