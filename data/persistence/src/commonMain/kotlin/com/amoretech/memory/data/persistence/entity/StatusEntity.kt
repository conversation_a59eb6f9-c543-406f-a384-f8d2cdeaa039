package com.amoretech.memory.data.persistence.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "StatusDB")
data class StatusEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "remoteId")
    val id: String,
    val type: String,
    val uri: String,
    @ColumnInfo(name = "createdAt")
    val createdAt: String,
    val content: String,
    val accountId: String?,
    val visibility: String,
    val sensitive: Boolean?,
    @ColumnInfo(name = "spoilerText")
    val spoiler: String,
    @ColumnInfo(name = "avatarUrl")
    val avatar: String,
    @ColumnInfo(name = "accountAddress")
    val accountHandle: String,
    @ColumnInfo(name = "applicationName")
    val appName: String,
    @ColumnInfo(name = "userName")
    val username: String,
    val repliesCount: Int?,
    val favouritesCount: Int?,
    val reblogsCount: Int?,
)
