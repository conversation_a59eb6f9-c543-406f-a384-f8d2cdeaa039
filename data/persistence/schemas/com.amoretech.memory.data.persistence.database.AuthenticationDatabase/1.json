{"formatVersion": 1, "database": {"version": 1, "identityHash": "54f85b7a1ae8ceb459dd2a7c000d4e38", "entities": [{"tableName": "Application", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`instance` TEXT NOT NULL, `clientId` TEXT NOT NULL, `clientSecret` TEXT NOT NULL, `redirectUri` TEXT NOT NULL, PRIMARY KEY(`instance`))", "fields": [{"fieldPath": "instance", "columnName": "instance", "affinity": "TEXT", "notNull": true}, {"fieldPath": "clientId", "columnName": "clientId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "clientSecret", "columnName": "clientSecret", "affinity": "TEXT", "notNull": true}, {"fieldPath": "redirectUri", "columnName": "redirectUri", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["instance"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '54f85b7a1ae8ceb459dd2a7c000d4e38')"]}}