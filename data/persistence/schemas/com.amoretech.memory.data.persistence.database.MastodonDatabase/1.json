{"formatVersion": 1, "database": {"version": 1, "identityHash": "9797e7e91726e8d2944e5b502278856b", "entities": [{"tableName": "instance_registration_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`domain` TEXT NOT NULL, `clientId` TEXT NOT NULL, `clientSecret` TEXT NOT NULL, PRIMARY KEY(`domain`))", "fields": [{"fieldPath": "domain", "columnName": "domain", "affinity": "TEXT", "notNull": true}, {"fieldPath": "clientId", "columnName": "clientId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "clientSecret", "columnName": "clientSecret", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["domain"]}}, {"tableName": "app_token_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`domain` TEXT NOT NULL, `accessToken` TEXT NOT NULL, `tokenType` TEXT NOT NULL, `scope` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`domain`))", "fields": [{"fieldPath": "domain", "columnName": "domain", "affinity": "TEXT", "notNull": true}, {"fieldPath": "accessToken", "columnName": "accessToken", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tokenType", "columnName": "tokenType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "scope", "columnName": "scope", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["domain"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '9797e7e91726e8d2944e5b502278856b')"]}}