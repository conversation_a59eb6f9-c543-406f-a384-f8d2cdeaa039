{"formatVersion": 1, "database": {"version": 1, "identityHash": "123a00632bc8dd9f7066917f53aa2fb2", "entities": [{"tableName": "FailedWrite", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `datetime` INTEGER NOT NULL, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "datetime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["key"]}}, {"tableName": "StatusDB", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`remoteId` TEXT NOT NULL, `type` TEXT NOT NULL, `uri` TEXT NOT NULL, `createdAt` TEXT NOT NULL, `content` TEXT NOT NULL, `accountId` TEXT, `visibility` TEXT NOT NULL, `sensitive` INTEGER, `spoilerText` TEXT NOT NULL, `avatarUrl` TEXT NOT NULL, `accountAddress` TEXT NOT NULL, `applicationName` TEXT NOT NULL, `userName` TEXT NOT NULL, `repliesCount` INTEGER, `favouritesCount` INTEGER, `reblogsCount` INTEGER, PRIMARY KEY(`remoteId`))", "fields": [{"fieldPath": "id", "columnName": "remoteId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uri", "columnName": "uri", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "accountId", "columnName": "accountId", "affinity": "TEXT"}, {"fieldPath": "visibility", "columnName": "visibility", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sensitive", "columnName": "sensitive", "affinity": "INTEGER"}, {"fieldPath": "spoiler", "columnName": "spoilerText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatarUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "accountHandle", "columnName": "accountAddress", "affinity": "TEXT", "notNull": true}, {"fieldPath": "appName", "columnName": "applicationName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "username", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "repliesCount", "columnName": "repliesCount", "affinity": "INTEGER"}, {"fieldPath": "favouritesCount", "columnName": "favouritesCount", "affinity": "INTEGER"}, {"fieldPath": "reblogsCount", "columnName": "reblogsCount", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["remoteId"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '123a00632bc8dd9f7066917f53aa2fb2')"]}}