import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.ksp)
    alias(libs.plugins.room)
    id("detekt")
    id("ktlint")
}

kotlin {

    /**
     * to suppress this warning.
     *
     * 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573
     */
    targets.configureEach {
        compilations.configureEach {
            compileTaskProvider.get().compilerOptions {
                freeCompilerArgs.add("-Xexpect-actual-classes")
            }
        }
    }

    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.data.persistence"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "persistenceKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(libs.kotlin.stdlib)
                implementation(libs.org.jetbrains.kotlinx.atomicfu)
                implementation(libs.multiplatform.settings)
                implementation(libs.multiplatform.settings.coroutines)
                implementation(libs.kotlinx.coroutines.core)
                implementation(libs.kotlinx.serialization.json)
                implementation(libs.io.insert.koin.core)
                implementation(libs.store)
                implementation(libs.androidx.room.runtime)
                implementation(libs.sqlite.bundled)
                implementation(libs.kotlinx.datetime)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
                implementation(libs.org.jetbrains.kotlinx.coroutines.test)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.io.insert.koin.android)
                implementation(libs.androidx.room.runtime)
            }
        }

        iosMain {
            dependencies {}
        }

        iosTest {
            dependencies {
                implementation(libs.org.jetbrains.kotlinx.coroutines.test)
            }
        }

        jvmMain {
            dependencies {}
        }

        jvmTest {
            dependencies {}
        }
    }
}

room {
    schemaDirectory("$projectDir/schemas")
}

// Apply KSP compiler dependency per target source set for clarity and control
dependencies {
    // KSP compiler for platform-specific targets - This is where actuals should be generated
    add("kspAndroid", libs.androidx.room.compiler)
    add("kspJvm", libs.androidx.room.compiler) // Add KSP for JVM target
    add("kspIosX64", libs.androidx.room.compiler) // KSP for iOS simulator (x64)
    add("kspIosArm64", libs.androidx.room.compiler) // KSP for iOS device (arm64)
    add("kspIosSimulatorArm64", libs.androidx.room.compiler) // KSP for iOS simulator (arm64)
}

// Optional: Task to ensure KSP common metadata task runs before platform compile tasks
// This can help with IDE recognition and build order issues
tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    val kspTaskName = "kspCommonMainKotlinMetadata"
    if (project.tasks.findByName(kspTaskName) != null && name != kspTaskName) {
        dependsOn(kspTaskName)
    }
}
