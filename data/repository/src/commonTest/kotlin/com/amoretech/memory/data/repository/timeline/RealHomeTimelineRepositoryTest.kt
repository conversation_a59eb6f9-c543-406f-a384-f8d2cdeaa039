package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.data.repository.timeline.fixtures.failureResponse
import com.amoretech.memory.data.repository.timeline.fixtures.fakeLocalStatus
import com.amoretech.memory.domain.timeline.model.StatusLocal
import com.amoretech.memory.domain.timeline.repository.FeedType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.test.TestResult
import kotlinx.coroutines.test.runTest
import org.mobilenativefoundation.store.core5.ExperimentalStoreApi
import org.mobilenativefoundation.store.store5.Store
import org.mobilenativefoundation.store.store5.StoreReadRequest
import org.mobilenativefoundation.store.store5.StoreReadResponse
import org.mobilenativefoundation.store.store5.StoreReadResponseOrigin
import kotlin.test.Test
import kotlin.test.assertTrue
import kotlin.test.fail

class RealHomeTimelineRepositoryTest {
    @Test
    fun successTest(): TestResult =
        runTest {
            val testRepo = RealHomeTimelineRepository(fakeSuccessStore)
            val result = testRepo.read(FeedType.Home).first()
            assertTrue { result is StoreReadResponse.Data }
            assertTrue { result.requireData().first() == fakeLocalStatus }
        }

    @Test
    fun failureTest(): TestResult =
        runTest {
            val testRepo = RealHomeTimelineRepository(fakeFailureStore)
            val result = testRepo.read(FeedType.Home).first()
            assertTrue { result is StoreReadResponse.Error.Message }
            assertTrue { result.errorMessageOrNull() == failureResponse.message }
        }
}

val fakeSuccessStore =
    object : Store<FeedType, List<StatusLocal>> {
        override suspend fun clear(key: FeedType) {
            TODO("Not yet implemented")
        }

        @ExperimentalStoreApi
        override suspend fun clear() {
            TODO("Not yet implemented")
        }

        override fun stream(request: StoreReadRequest<FeedType>): Flow<StoreReadResponse<List<StatusLocal>>> =
            when (request.key.type) {
                FeedType.Home.type -> {
                    flow {
                        emit(
                            StoreReadResponse.Data(
                                listOf(fakeLocalStatus),
                                StoreReadResponseOrigin.Cache,
                            ),
                        )
                    }
                }

                else -> {
                    fail("wrong response")
                }
            }
    }

val fakeFailureStore =
    object : Store<FeedType, List<StatusLocal>> {
        override suspend fun clear(key: FeedType) {
            TODO("Not yet implemented")
        }

        @ExperimentalStoreApi
        override suspend fun clear() {
            TODO("Not yet implemented")
        }

        override fun stream(request: StoreReadRequest<FeedType>): Flow<StoreReadResponse<List<StatusLocal>>> =
            when (request.key.type) {
                FeedType.Home.type -> {
                    flow {
                        emit(failureResponse)
                    }
                }

                else -> {
                    fail("wrong response")
                }
            }
    }
