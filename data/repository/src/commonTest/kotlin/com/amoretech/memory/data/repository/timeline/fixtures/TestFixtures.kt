package com.amoretech.memory.data.repository.timeline.fixtures

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.network.model.Application
import com.amoretech.memory.data.network.model.AvailableInstance
import com.amoretech.memory.data.network.model.Instance
import com.amoretech.memory.data.network.model.NewOauthApplication
import com.amoretech.memory.data.network.model.Privacy
import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.data.network.model.Token
import com.amoretech.memory.data.persistence.dao.ApplicationDao
import com.amoretech.memory.data.persistence.entity.ApplicationEntity
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorage
import com.amoretech.memory.domain.timeline.model.StatusLocal
import com.amoretech.memory.domain.timeline.model.Visibility
import com.amoretech.memory.domain.timeline.repository.FeedType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.mobilenativefoundation.store.store5.StoreReadResponse
import org.mobilenativefoundation.store.store5.StoreReadResponseOrigin

class FakeAuthStorage(
    initialServers: List<String> = emptyList(),
) : DodoAuthStorage {
    private val _authorizedServersFlow = MutableStateFlow(initialServers)
    override val authorizedServersFlow: Flow<List<String>> = _authorizedServersFlow.asStateFlow()

    override var currentDomain: String? = null

    private val tokens = mutableMapOf<String, String>()

    override suspend fun saveAccessToken(
        server: String,
        token: String,
    ) {
        tokens[server] = token
        _authorizedServersFlow.value = tokens.keys.toList()
    }

    override fun getAccessToken(server: String): String = tokens[server] ?: ""

    override fun clearAccessToken(server: String) {
        tokens.remove(server)
        _authorizedServersFlow.value = tokens.keys.toList()
    }
}

class FakeApplicationDao : ApplicationDao {
    private val applications = mutableMapOf<String, ApplicationEntity>()
    private val _applicationsFlow = MutableStateFlow<List<ApplicationEntity>>(emptyList())

    override suspend fun insertApplication(application: ApplicationEntity) {
        applications[application.instance] = application
        _applicationsFlow.value = applications.values.toList()
    }

    override fun selectAllApplications(): Flow<List<ApplicationEntity>> = _applicationsFlow.asStateFlow()

    override suspend fun deleteAllApplications() {
        applications.clear()
        _applicationsFlow.value = emptyList()
    }

    override suspend fun selectByServer(instance: String): ApplicationEntity? = applications[instance]
}

val failureResponse = StoreReadResponse.Error.Message("We failed", StoreReadResponseOrigin.Cache)

val fakeLocalStatus =
    StatusLocal(
        "",
        FeedType.Home,
        "",
        0,
        0,
        0,
        "",
        null,
        false,
        "",
        Visibility.UNLISTED.name,
        "",
        "",
        "",
    )

val fakeStatus =
    Status(
        "",
        "",
        "",
        null,
        "",
        Privacy.DIRECT,
        false,
        "",
    )

val fakeApi =
    object : MastodonApi {
        override suspend fun listInstances(): Result<List<AvailableInstance>> {
            TODO("Not yet implemented")
        }

        override suspend fun createApplication(
            domain: String,
            clientName: String,
            redirectUris: String,
            scopes: String,
            website: String?,
        ): Result<NewOauthApplication> {
            TODO("Not yet implemented")
        }

        override suspend fun createAccessToken(
            domain: String,
            clientId: String,
            clientSecret: String,
            redirectUri: String,
            grantType: String,
            code: String,
            scope: String,
        ): Result<Token> {
            TODO("Not yet implemented")
        }

        override suspend fun verifyApplication(): Result<Application> {
            TODO("Not yet implemented")
        }

        override suspend fun getInstance(domain: String?): Result<Instance> {
            TODO("Not yet implemented")
        }

        override suspend fun getHomeFeed(
            domain: String,
            accessToken: String,
        ): Result<List<Status>> =
            Result.success(
                listOf<Status>(
                    fakeStatus,
                ),
            )
    }
