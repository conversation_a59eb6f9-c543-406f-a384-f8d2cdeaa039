package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.data.repository.AuthenticationRepositoryImpl
import com.amoretech.memory.data.repository.timeline.fixtures.FakeApplicationDao
import com.amoretech.memory.data.repository.timeline.fixtures.FakeAuthStorage
import com.amoretech.memory.data.repository.timeline.fixtures.fakeApi
import com.amoretech.memory.domain.authentication.model.NewAppOAuthToken
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

class AuthenticationRepositoryTest {
    private val ioContext = kotlinx.coroutines.Dispatchers.Unconfined

    private fun createRepo(initialServers: List<String> = emptyList()): AuthenticationRepositoryImpl {
        val fakeDao = FakeApplicationDao()
        val fakeStorage = FakeAuthStorage(initialServers)
        return AuthenticationRepositoryImpl(
            mastodonApi = fakeApi,
            applicationDao = fakeDao,
            settings = fakeStorage,
            ioCoroutineContext = ioContext,
        )
    }

    @Test
    fun testSaveAndGetApplication() =
        runTest {
            val repo = createRepo()

            val newAppToken =
                NewAppOAuthToken(
                    clientId = "clientId",
                    clientSecret = "clientSecret",
                    redirectUri = "https://redirect.uri",
                )

            repo.saveApplication(newAppToken, "test.server")

            val saved = repo.getApplicationOAuthToken("test.server")
            assertEquals("clientId", saved?.clientId)
            assertEquals("clientSecret", saved?.clientSecret)
            assertEquals("https://redirect.uri", saved?.redirectUri)
        }
}
