package com.amoretech.memory.data.repository.serverlist.mapper

import com.amoretech.memory.fedidb.dto.FedIDBInstance
import com.amoretech.memory.fedidb.dto.Location
import com.amoretech.memory.fedidb.dto.Software
import com.amoretech.memory.fedidb.dto.Stats
import com.amoretech.memory.serverlist.model.FedIDBModel
import com.amoretech.memory.serverlist.model.LocationModel
import com.amoretech.memory.serverlist.model.SoftwareModel
import com.amoretech.memory.serverlist.model.StatsModel
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

fun FedIDBInstance.toDomainModel(): FedIDBModel {
    fun String.toSafeLocalDateTime(): LocalDateTime =
        try {
            // 1. Parse the full ISO string into an Instant (a point in universal time)
            val instant = Instant.parse(this)
            // 2. Convert the Instant to a LocalDateTime in the system's default timezone
            instant.toLocalDateTime(TimeZone.currentSystemDefault())
        } catch (e: Exception) {
            // Catches parsing errors like IllegalArgumentException
            // On failure, return the current time as a fallback
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        }

    return FedIDBModel(
        id = this.id,
        domain = this.domain,
        openRegistration = this.openRegistration,
        description = this.description ?: "No description available.",
        bannerUrl = this.bannerUrl,
        location = this.location.toDomainModel(), // Use the location mapper
        software = this.software.toDomainModel(), // Use the software mapper
        stats = this.stats.toDomainModel(), // Use the stats mapper
        firstSeenAt = this.firstSeenAt.toSafeLocalDateTime(),
        lastSeenAt = this.lastSeenAt.toSafeLocalDateTime(),
    )
}

// --- Mapper for Location ---
fun Location.toDomainModel(): LocationModel =
    LocationModel(
        city = this.city ?: "Unknown City",
        country = this.country ?: "Unknown Country",
    )

// --- Mapper for Software ---
fun Software.toDomainModel(): SoftwareModel =
    SoftwareModel(
        id = this.id ?: 0,
        name = this.name.orEmpty(),
        version = this.version,
        url = this.url.orEmpty(),
        slug = this.slug,
    )

// --- Mapper for Stats ---
fun Stats.toDomainModel(): StatsModel =
    StatsModel(
        userCount = this.userCount ?: 0L,
        statusCount = this.statusCount ?: 0L,
        monthlyActiveUsers = this.monthlyActiveUsers ?: 0L,
    )

// --- Mapper for a list of DTOs ---
fun List<FedIDBInstance>.toDomainModelList(): List<FedIDBModel> = this.map { it.toDomainModel() }
