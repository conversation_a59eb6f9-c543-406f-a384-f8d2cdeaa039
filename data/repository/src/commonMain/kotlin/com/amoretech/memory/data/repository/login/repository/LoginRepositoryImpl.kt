package com.amoretech.memory.data.repository.login.repository

import com.amoretech.memory.data.repository.login.mapper.toModel
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.login.model.OAuthTokenModel
import com.amoretech.memory.login.model.UserAccountModel
import com.amoretech.memory.login.repository.LoginRepository
import com.amoretech.memory.mastodon.api.MastodonApi
import com.amoretech.memory.mastodon.constant.MastodonConstant
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class LoginRepositoryImpl(
    private val mastodonApi: MastodonApi,
) : LoginRepository {
    override fun buildAuthorizationUrl(
        domain: String,
        clientId: String,
        redirectUri: String?,
        scopes: String?,
    ): String =
        mastodonApi.buildAuthorizationUrl(
            domain = domain,
            clientId = clientId,
            redirectUri = redirectUri ?: MastodonConstant.DEFAULT_REDIRECT_URI,
            scopes = scopes ?: MastodonConstant.DEFAULT_SCOPES,
        )

    override fun exchangeCodeForToken(
        domain: String,
        clientId: String,
        clientSecret: String,
        code: String,
        redirectUri: String?,
    ): Flow<ApiResult<OAuthTokenModel>> =
        mastodonApi
            .exchangeCodeForToken(
                domain = domain,
                clientId = clientId,
                clientSecret = clientSecret,
                code = code,
                redirectUri = redirectUri ?: MastodonConstant.DEFAULT_REDIRECT_URI,
            ).map { result ->
                when (result) {
                    is ApiResult.Success -> ApiResult.Success(result.data.toModel())
                    is ApiResult.Error -> result
                }
            }

    override suspend fun verifyCredentials(
        domain: String,
        accessToken: String,
    ): Flow<ApiResult<UserAccountModel>> =
        mastodonApi
            .verifyCredentials(domain, accessToken)
            .map { result ->
                when (result) {
                    is ApiResult.Success -> ApiResult.Success(result.data.toModel())
                    is ApiResult.Error -> result
                }
            }
}
