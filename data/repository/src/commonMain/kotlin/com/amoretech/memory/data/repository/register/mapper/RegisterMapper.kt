package com.amoretech.memory.data.repository.register.mapper

import com.amoretech.memory.mastodon.dto.AppRegistrationResponse
import com.amoretech.memory.mastodon.dto.InstanceRuleResponse
import com.amoretech.memory.mastodon.dto.RuleTranslation
import com.amoretech.memory.mastodon.dto.TokenResponse
import com.amoretech.memory.mastodon.dto.UserTokenResponse
import com.amoretech.memory.register.model.AppRegistrationModel
import com.amoretech.memory.register.model.InstanceRuleModel
import com.amoretech.memory.register.model.RuleTranslationModel
import com.amoretech.memory.register.model.TokenModel

fun InstanceRuleResponse.toModel(): InstanceRuleModel =
    InstanceRuleModel(
        id = this.id,
        text = this.text,
        hint = this.hint,
        translations = this.translations?.map { it.key to it.value.toModel() }?.toMap() ?: emptyMap(),
    )

fun RuleTranslation.toModel(): RuleTranslationModel =
    RuleTranslationModel(
        text = this.text,
        hint = this.hint,
    )

fun AppRegistrationResponse.toModel(): AppRegistrationModel =
    AppRegistrationModel(
        clientId = this.clientId,
        clientSecret = this.clientSecret,
    )

fun TokenResponse.toModel(): TokenModel =
    TokenModel(
        accessToken = this.accessToken,
        tokenType = this.tokenType,
        scope = this.scope,
        createdAt = this.createdAt,
    )

fun UserTokenResponse.toModel(): TokenModel =
    TokenModel(
        accessToken = this.accessToken,
        tokenType = this.tokenType,
        scope = this.scope,
        createdAt = this.createdAt,
    )
