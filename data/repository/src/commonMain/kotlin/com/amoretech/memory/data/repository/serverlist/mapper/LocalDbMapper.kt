package com.amoretech.memory.data.repository.serverlist.mapper

import com.amoretech.memory.data.persistence.entity.AppTokenEntity
import com.amoretech.memory.data.persistence.entity.InstanceRegistrationEntity
import com.amoretech.memory.serverlist.model.AppTokenModel
import com.amoretech.memory.serverlist.model.InstanceRegistrationModel

fun InstanceRegistrationEntity.toModel(): InstanceRegistrationModel =
    InstanceRegistrationModel(
        domain = this.domain,
        clientId = this.clientId,
        clientSecret = this.clientSecret,
    )

fun InstanceRegistrationModel.toEntity(): InstanceRegistrationEntity =
    InstanceRegistrationEntity(
        domain = this.domain,
        clientId = this.clientId,
        clientSecret = this.clientSecret,
    )

fun AppTokenEntity.toModel(): AppTokenModel =
    AppTokenModel(
        domain = this.domain,
        accessToken = this.accessToken,
        tokenType = this.tokenType,
        scope = this.scope,
        createdAt = this.createdAt,
    )

fun AppTokenModel.toEntity(): AppTokenEntity =
    AppTokenEntity(
        domain = this.domain,
        accessToken = this.accessToken,
        tokenType = this.tokenType,
        scope = this.scope,
        createdAt = this.createdAt,
    )
