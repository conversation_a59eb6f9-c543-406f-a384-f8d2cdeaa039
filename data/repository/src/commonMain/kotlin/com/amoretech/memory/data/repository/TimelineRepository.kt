package com.amoretech.memory.data.repository

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository

interface TimelineRepository {
    suspend fun getHomeTimeline(
        maxId: String? = null,
        sinceId: String? = null,
        minId: String? = null,
        limit: Int? = null,
    ): Result<List<Status>>

    // TODO: Implement these when the API endpoints are available
    suspend fun getPublicTimeline(
        local: Boolean? = null,
        remote: Boolean? = null,
        onlyMedia: Boolean? = null,
        maxId: String? = null,
        sinceId: String? = null,
        minId: String? = null,
        limit: Int? = null,
    ): Result<List<Status>>

    suspend fun getHashtagTimeline(
        hashtag: String,
        local: Boolean? = null,
        onlyMedia: Boolean? = null,
        maxId: String? = null,
        sinceId: String? = null,
        minId: String? = null,
        limit: Int? = null,
    ): Result<List<Status>>

    suspend fun getListTimeline(
        listId: String,
        maxId: String? = null,
        sinceId: String? = null,
        minId: String? = null,
        limit: Int? = null,
    ): Result<List<Status>>
}

internal class TimelineRepositoryImpl(
    private val mastodonApi: MastodonApi,
    private val authRepository: AuthenticationRepository,
) : TimelineRepository {
    private fun requireAuth(): Pair<String, String> {
        val domain =
            authRepository.selectedServer
                ?: throw IllegalStateException("No server selected")
        val accessToken =
            authRepository.getCurrentAccessToken()
                ?: throw IllegalStateException("No access token available")
        return domain to accessToken
    }

    override suspend fun getHomeTimeline(
        maxId: String?,
        sinceId: String?,
        minId: String?,
        limit: Int?,
    ): Result<List<Status>> {
        val (domain, accessToken) = requireAuth()
        return mastodonApi.getHomeFeed(domain, accessToken)
    }

    override suspend fun getPublicTimeline(
        local: Boolean?,
        remote: Boolean?,
        onlyMedia: Boolean?,
        maxId: String?,
        sinceId: String?,
        minId: String?,
        limit: Int?,
    ): Result<List<Status>> {
        // TODO: Implement when getPublicTimeline API is available
        return Result.failure(NotImplementedError("getPublicTimeline not yet implemented"))
    }

    override suspend fun getHashtagTimeline(
        hashtag: String,
        local: Boolean?,
        onlyMedia: Boolean?,
        maxId: String?,
        sinceId: String?,
        minId: String?,
        limit: Int?,
    ): Result<List<Status>> {
        // TODO: Implement when getHashtagTimeline API is available
        return Result.failure(NotImplementedError("getHashtagTimeline not yet implemented"))
    }

    override suspend fun getListTimeline(
        listId: String,
        maxId: String?,
        sinceId: String?,
        minId: String?,
        limit: Int?,
    ): Result<List<Status>> {
        // TODO: Implement when getListTimeline API is available
        return Result.failure(NotImplementedError("getListTimeline not yet implemented"))
    }
}
