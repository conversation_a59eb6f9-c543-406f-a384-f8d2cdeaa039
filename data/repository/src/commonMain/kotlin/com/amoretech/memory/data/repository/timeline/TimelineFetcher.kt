package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.persistence.entity.StatusEntity
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorage
import com.amoretech.memory.domain.timeline.repository.FeedType
import org.mobilenativefoundation.store.store5.Fetcher

/**
 * Wrapper for [MastodonApi.getHomeFeed] while also getting an auth token from storage
 * and mapping result to list of [StatusEntity]
 */
fun MastodonApi.timelineFetcher(authStorage: DodoAuthStorage): Fetcher<FeedType, List<StatusEntity>> =
    Fetcher.of { key: FeedType ->
        when (key) {
            is FeedType.Home -> {
                getHomeFeed(
                    authStorage.currentDomain!!,
                    authStorage.getAccessToken(authStorage.currentDomain!!)!!,
                ).getOrThrow()
                    .map { it.toEntity() }
            }
        }
    }
