package com.amoretech.memory.data.repository.serverlist.repository

import com.amoretech.memory.data.persistence.dao.MastodonDao
import com.amoretech.memory.data.repository.serverlist.mapper.toDomainModel
import com.amoretech.memory.data.repository.serverlist.mapper.toEntity
import com.amoretech.memory.data.repository.serverlist.mapper.toModel
import com.amoretech.memory.fedidb.api.FedIDBApi
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.serverlist.model.AppTokenModel
import com.amoretech.memory.serverlist.model.FedIDBModel
import com.amoretech.memory.serverlist.model.InstanceRegistrationModel
import com.amoretech.memory.serverlist.repository.FedIDBRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

internal class FedIDBRepositoryImpl(
    private val fedIDBApi: FedIDBApi,
    private val mastodonDao: MastodonDao,
) : FedIDBRepository {
    override fun getInstances(limit: Int): Flow<ApiResult<List<FedIDBModel>>> =
        fedIDBApi
            .getInstances(limit)
            .map { apiResult ->
                when (apiResult) {
                    is ApiResult.Error -> {
                        apiResult
                    }

                    is ApiResult.Success -> {
                        val dtoList = apiResult.data
                        val modelList = dtoList.map { it.toDomainModel() }
                        ApiResult.Success(modelList)
                    }
                }
            }

    override suspend fun checkHasInstanceRegister(domain: String): Boolean {
        val response = mastodonDao.getInstanceRegistrationEntity(domain)
        return response != null
    }

    override suspend fun saveInstanceRegistrationModel(model: InstanceRegistrationModel) {
        mastodonDao.saveInstanceRegistrationEntity(registration = model.toEntity())
    }

    override suspend fun saveAccessToken(model: AppTokenModel) {
        mastodonDao.saveAppTokenEntity(model.toEntity())
    }

    override suspend fun getAccessTokenByDomain(domain: String): AppTokenModel? {
        val result = mastodonDao.getAppTokenEntity(domain)
        return result?.toModel()
    }
}
