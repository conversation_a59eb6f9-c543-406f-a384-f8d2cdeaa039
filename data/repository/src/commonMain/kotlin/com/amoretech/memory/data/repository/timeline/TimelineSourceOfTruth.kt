package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.data.persistence.database.TimelineDatabase
import com.amoretech.memory.data.persistence.entity.StatusEntity
import com.amoretech.memory.domain.timeline.model.StatusLocal
import com.amoretech.memory.domain.timeline.repository.FeedType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import org.mobilenativefoundation.store.store5.SourceOfTruth

fun TimelineDatabase.asSourceOfTruth(): SourceOfTruth<FeedType, List<StatusEntity>, List<StatusLocal>> =
    SourceOfTruth.of(
        reader = reader(),
        writer = { key, input: List<StatusEntity> ->
            withContext(Dispatchers.IO) {
                input.forEach { item ->
                    tryWriteItem(item, key)
                }
            }
        },
    )

private fun TimelineDatabase.reader(): (FeedType) -> Flow<List<StatusLocal>> =
    { key: FeedType ->
        // This function *returns* the lambda
        when (key) {
            is FeedType.Home -> {
                // The lambda's body returns the Flow based on the key
                statusDao().selectHomeItems().map { entityList ->
                    entityList.map { entity -> entity.toLocal(key) }
                }
            }
            // Add cases for other FeedTypes and their corresponding DAO queries
//            else -> {
//                // Example: If you had a query for 'Other' feed types
//                // statusDao().selectOtherItems(key.id)
//                emptyFlow() // Return an empty flow or handle appropriately if no query exists
//            }
        }
    }

// private fun TimelineQueries.homeItemsAsLocal(key: FeedType) =  selectHomeItems()
//    .asFlow()
//    .mapToList(Dispatchers.Default)
//    .map {
//        it.ifEmpty { return@map null } // treat empty list as no result otherwise
//        it.map { item -> item.toLocal(key) }
//    }

suspend fun TimelineDatabase.tryWriteItem(
    it: StatusEntity,
    type: FeedType,
): Boolean =
    runCatching {
        this.statusDao().insertFeedItem(
            it.copy(type = type.type),
        )
    }.fold(onSuccess = {
        true
    }, onFailure = {
        // TODO: Add proper logging
        false
    })
