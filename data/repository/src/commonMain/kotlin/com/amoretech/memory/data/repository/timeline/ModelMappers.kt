package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.data.persistence.entity.StatusEntity
import com.amoretech.memory.domain.timeline.model.Account
import com.amoretech.memory.domain.timeline.model.StatusLocal
import com.amoretech.memory.domain.timeline.repository.FeedType

/**
 * Maps a Room StatusEntity object to a domain layer StatusLocal object.
 *
 * @param key The FeedType associated with this status, used for the StatusLocal model.
 * @return A StatusLocal object.
 */
fun StatusEntity.toLocal(
    key: FeedType, // Pass the FeedType from the reader logic
): StatusLocal =
    StatusLocal(
        remoteId = this.id, // Mapping StatusEntity.id to StatusLocal.remoteId
        feedType = key, // Setting the feed type based on the key
        createdAt = this.createdAt,
        // Mapping nullable Ints to Long with a default of 0
        repliesCount = this.repliesCount?.toLong() ?: 0,
        reblogsCount = this.reblogsCount?.toLong() ?: 0,
        favoritesCount = this.favouritesCount?.toLong() ?: 0, // Mapping favouritesCount to favoritesCount
        content = this.content,
        // Creating the Account object from StatusEntity fields
        account =
            Account(
                id = this.accountId.orEmpty(),
                username = this.username, // Mapping StatusEntity.username to Account.username
                acct = this.accountHandle, // Mapping StatusEntity.accountHandle to Account.accountAddress
                avatar = this.avatar, // Mapping StatusEntity.avatar to Account.avatarUrl
            ),
        // Mapping nullable Boolean to non-nullable Boolean with a default of false
        sensitive = this.sensitive ?: false,
        // Mapping spoiler (non-nullable String) to spoilerText (nullable String?)
        spoilerText = this.spoiler.takeIf { it.isNotBlank() }, // Use takeIf to make it null if blank/empty
        visibility = this.visibility,
        avatarUrl = this.avatar, // Direct mapping for avatarUrl field in StatusLocal
        accountAddress = this.accountHandle, // Direct mapping for accountAddress field in StatusLocal
        userName = this.username, // Direct mapping for userName field in StatusLocal
    )

fun Status.toEntity() =
    StatusEntity(
        id = this.id, // Direct mapping for the primary key
        type = FeedType.Home.type, // Placeholder: Type must be set based on FeedType before insertion
        uri = this.uri,
        createdAt = this.createdAt,
        content = this.content,
        accountId = this.account?.id, // Map nested account ID, handle null account
        visibility = this.visibility.name, // Map Privacy enum to String name
        sensitive = this.sensitive, // Direct mapping (Boolean to Boolean?)
        spoiler = this.spoilerText, // Map spoilerText (non-nullable String) to spoiler (non-nullable String)
        avatar = this.account?.avatar ?: "", // Map nested account avatar, handle null account
        accountHandle = this.account?.acct ?: "", // Map nested account handle, handle null account
        username = this.account?.username ?: "", // Map nested account username, handle null account
        repliesCount = this.repliesCount, // Map nullable Ints
        favouritesCount = this.favouritesCount, // Map nullable Ints
        reblogsCount = this.reblogsCount,
        appName = this.application?.name.orEmpty(),
    )
