package com.amoretech.memory.data.repository.register.repository

import com.amoretech.memory.data.repository.register.mapper.toModel
import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.mastodon.api.MastodonApi
import com.amoretech.memory.register.model.AppRegistrationModel
import com.amoretech.memory.register.model.InstanceRuleModel
import com.amoretech.memory.register.model.TokenModel
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class RegistrationRepositoryImpl(
    private val mastodonApi: MastodonApi,
) : RegistrationRepository {
    override fun getInstanceRules(domain: String): Flow<ApiResult<List<InstanceRuleModel>>> =
        mastodonApi.getInstanceRules(domain).map { result ->
            when (result) {
                is ApiResult.Success -> ApiResult.Success(result.data.map { it.toModel() })
                is ApiResult.Error -> result
            }
        }

    override fun registerApp(domain: String): Flow<ApiResult<AppRegistrationModel>> =
        mastodonApi.registerApp(domain).map { result ->
            when (result) {
                is ApiResult.Success -> ApiResult.Success(result.data.toModel())
                is ApiResult.Error -> result
            }
        }

    override suspend fun getAppToken(
        domain: String,
        clientId: String,
        clientSecret: String,
    ): Flow<ApiResult<TokenModel>> =
        mastodonApi.getAppToken(domain, clientId, clientSecret).map { result ->
            when (result) {
                is ApiResult.Success -> ApiResult.Success(result.data.toModel())
                is ApiResult.Error -> result
            }
        }

    override suspend fun createAccount(
        domain: String,
        appToken: String,
        userName: String,
        emailAddress: String,
        password: String,
        isAgreed: Boolean,
        localeString: String,
    ): Flow<ApiResult<TokenModel>> =
        mastodonApi
            .createAccount(
                domain,
                appToken,
                userName,
                emailAddress,
                password,
                isAgreed,
                localeString,
            ).map { result ->
                when (result) {
                    is ApiResult.Success -> ApiResult.Success(result.data.toModel())
                    is ApiResult.Error -> result
                }
            }

    override suspend fun checkUsernameAvailability(
        domain: String,
        username: String,
    ): Flow<ApiResult<Boolean>> = mastodonApi.checkUsernameAvailability(domain, username)

    override suspend fun resendConfirmation(
        domain: String,
        userToken: String,
    ): Flow<ApiResult<Boolean>> =
        mastodonApi.resendConfirmation(domain, userToken).map { result ->
            when (result) {
                is ApiResult.Success -> ApiResult.Success(true)
                is ApiResult.Error -> ApiResult.Error(result.message, result.cause)
            }
        }
}
