package com.amoretech.memory.data.repository

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.persistence.dao.ApplicationDao
import com.amoretech.memory.data.persistence.entity.ApplicationEntity
import com.amoretech.memory.data.persistence.localstorage.DodoAuthStorage
import com.amoretech.memory.domain.authentication.model.ApplicationOAuthToken
import com.amoretech.memory.domain.authentication.model.NewAppOAuthToken
import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.transform
import kotlinx.coroutines.withContext
import kotlin.coroutines.CoroutineContext

internal class AuthenticationRepositoryImpl(
    private val mastodonApi: MastodonApi,
    private val applicationDao: ApplicationDao,
    private val settings: DodoAuthStorage,
    private val ioCoroutineContext: CoroutineContext,
) : AuthenticationRepository {
    override suspend fun createApplicationClient(
        domain: String,
        clientName: String,
        redirectUris: String,
        scopes: String,
        website: String?,
    ): NewAppOAuthToken? {
        val application =
            mastodonApi
                .createApplication(
                    domain = domain,
                    clientName = clientName,
                    redirectUris = redirectUris,
                    scopes = scopes,
                    website = website,
                ).getOrNull()

        return application?.let {
            NewAppOAuthToken(
                clientId = it.clientId,
                clientSecret = it.clientSecret,
                redirectUri = redirectUris,
            )
        }
    }

    override suspend fun saveApplication(
        token: NewAppOAuthToken,
        domain: String,
    ) {
        // save our new application oauth token to our DB
        applicationDao.insertApplication(
            ApplicationEntity(
                instance = domain,
                clientId = token.clientId,
                clientSecret = token.clientSecret,
                redirectUri = token.redirectUri,
            ),
        )

        // Update what server the user is currently on
        settings.currentDomain = domain
    }

    override suspend fun getApplicationOAuthToken(server: String): ApplicationOAuthToken? =
        applicationDao.selectByServer(server)?.let {
            ApplicationOAuthToken(
                server = it.instance,
                clientId = it.clientId,
                clientSecret = it.clientSecret,
                redirectUri = it.redirectUri,
            )
        }

    override suspend fun createAccessToken(
        authCode: String,
        server: String,
        scope: String,
        grantType: String,
    ): String? =
        getApplicationOAuthToken(server)?.let { oAuthToken ->
            mastodonApi
                .createAccessToken(
                    domain = server,
                    clientId = oAuthToken.clientId,
                    clientSecret = oAuthToken.clientSecret,
                    redirectUri = oAuthToken.redirectUri,
                    grantType = grantType,
                    code = authCode,
                    scope = scope,
                ).getOrNull()
                ?.accessToken
        }

    override suspend fun saveAccessToken(
        server: String,
        token: String,
    ) {
        withContext(ioCoroutineContext) {
            settings.saveAccessToken(server = server, token = token)
        }
    }

    override val selectedServer: String? = settings.currentDomain

    override fun isAccessTokenPresent(): Flow<Boolean> =
        settings.authorizedServersFlow.transform { servers ->
            emit(servers.isNotEmpty())
        }

    override fun removeAccessToken(server: String) {
        settings.currentDomain = null
        settings.clearAccessToken(server)
    }

    override fun getCurrentAccessToken(): String? {
        val currentServer = selectedServer ?: return null
        return settings.getAccessToken(currentServer)
    }

    override suspend fun verifyCurrentCredentials(): Boolean {
        val currentServer = selectedServer ?: return false
        val accessToken = getCurrentAccessToken() ?: return false

        // TODO: Implement when verifyCredentials API is available
        // For now, just check if we have a token
        return accessToken.isNotBlank()
    }
}
