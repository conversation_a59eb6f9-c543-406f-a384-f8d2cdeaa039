package com.amoretech.memory.data.repository.login.mapper

import com.amoretech.memory.login.model.EmojiModel
import com.amoretech.memory.login.model.FieldModel
import com.amoretech.memory.login.model.OAuthTokenModel
import com.amoretech.memory.login.model.RoleModel
import com.amoretech.memory.login.model.SourceModel
import com.amoretech.memory.login.model.UserAccountModel
import com.amoretech.memory.mastodon.dto.AccountResponse
import com.amoretech.memory.mastodon.dto.EmojiDto
import com.amoretech.memory.mastodon.dto.FieldDto
import com.amoretech.memory.mastodon.dto.OAuthTokenResponse
import com.amoretech.memory.mastodon.dto.RoleDto
import com.amoretech.memory.mastodon.dto.SourceDto

fun OAuthTokenResponse.toModel() =
    OAuthTokenModel(
        accessToken = this.accessToken,
        tokenType = this.tokenType,
        scope = this.scope,
        createdAt = this.createdAt,
    )

fun AccountResponse.toModel() =
    UserAccountModel(
        id = id,
        username = username,
        acct = acct,
        displayName = displayName,
        locked = locked,
        bot = bot,
        discoverable = discoverable,
        indexable = indexable,
        group = group,
        createdAt = createdAt,
        note = note,
        url = url,
        uri = uri,
        avatar = avatar,
        avatarStatic = avatarStatic,
        header = header,
        headerStatic = headerStatic,
        followersCount = followersCount,
        followingCount = followingCount,
        statusesCount = statusesCount,
        lastStatusAt = lastStatusAt,
        hideCollections = hideCollections,
        noindex = noindex,
        source = source?.toModel(),
        emojis = emojis.map { it.toModel() },
        roles = roles.map { it.toModel() },
        fields = fields.map { it.toModel() },
        role = role?.toModel(),
    )

fun SourceDto.toModel() =
    SourceModel(
        privacy = privacy,
        sensitive = sensitive,
        language = language,
        note = note,
        fields = fields.map { it.toModel() },
        followRequestsCount = followRequestsCount,
        hideCollections = hideCollections,
        discoverable = discoverable,
        indexable = indexable,
        attributionDomains = attributionDomains,
    )

fun EmojiDto.toModel() =
    EmojiModel(
        shortcode = shortcode,
        url = url,
        staticUrl = staticUrl,
    )

fun RoleDto.toModel() =
    RoleModel(
        id = id,
        name = name,
        permissions = permissions,
        color = color,
        highlighted = highlighted,
    )

fun FieldDto.toModel() =
    FieldModel(
        name = name,
        value = value,
        verifiedAt = verifiedAt,
    )
