package com.amoretech.memory.data.repository

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository

interface StatusRepository {
    // For now, only implement basic status operations that we can simulate
    // TODO: Implement these when the actual Mastodon API endpoints are added
    suspend fun createStatus(
        content: String,
        inReplyToId: String? = null,
        mediaIds: List<String>? = null,
        sensitive: Boolean? = null,
        spoilerText: String? = null,
        visibility: String? = null,
        language: String? = null,
        scheduledAt: String? = null,
    ): Result<Status>

    suspend fun favouriteStatus(statusId: String): Result<Status>

    suspend fun unfavouriteStatus(statusId: String): Result<Status>

    suspend fun reblogStatus(
        statusId: String,
        visibility: String? = null,
    ): Result<Status>

    suspend fun unreblogStatus(statusId: String): Result<Status>

    suspend fun bookmarkStatus(statusId: String): Result<Status>

    suspend fun unbookmarkStatus(statusId: String): Result<Status>
}

internal class StatusRepositoryImpl(
    private val mastodonApi: MastodonApi,
    private val authRepository: AuthenticationRepository,
) : StatusRepository {
    private fun requireAuth(): Pair<String, String> {
        val domain =
            authRepository.selectedServer
                ?: throw IllegalStateException("No server selected")
        val accessToken =
            authRepository.getCurrentAccessToken()
                ?: throw IllegalStateException("No access token available")
        return domain to accessToken
    }

    override suspend fun createStatus(
        content: String,
        inReplyToId: String?,
        mediaIds: List<String>?,
        sensitive: Boolean?,
        spoilerText: String?,
        visibility: String?,
        language: String?,
        scheduledAt: String?,
    ): Result<Status> {
        // TODO: Implement when createStatus API is available
        // For now, return a mock status
        return Result.failure(NotImplementedError("createStatus not yet implemented"))
    }

    override suspend fun favouriteStatus(statusId: String): Result<Status> {
        // TODO: Implement when favouriteStatus API is available
        // For now, return a mock status with favourited = true
        return Result.failure(NotImplementedError("favouriteStatus not yet implemented"))
    }

    override suspend fun unfavouriteStatus(statusId: String): Result<Status> {
        // TODO: Implement when unfavouriteStatus API is available
        return Result.failure(NotImplementedError("unfavouriteStatus not yet implemented"))
    }

    override suspend fun reblogStatus(
        statusId: String,
        visibility: String?,
    ): Result<Status> {
        // TODO: Implement when reblogStatus API is available
        return Result.failure(NotImplementedError("reblogStatus not yet implemented"))
    }

    override suspend fun unreblogStatus(statusId: String): Result<Status> {
        // TODO: Implement when unreblogStatus API is available
        return Result.failure(NotImplementedError("unreblogStatus not yet implemented"))
    }

    override suspend fun bookmarkStatus(statusId: String): Result<Status> {
        // TODO: Implement when bookmarkStatus API is available
        return Result.failure(NotImplementedError("bookmarkStatus not yet implemented"))
    }

    override suspend fun unbookmarkStatus(statusId: String): Result<Status> {
        // TODO: Implement when unbookmarkStatus API is available
        return Result.failure(NotImplementedError("unbookmarkStatus not yet implemented"))
    }
}
