package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.persistence.database.TimelineDatabase
import com.amoretech.memory.data.persistence.entity.StatusEntity
import com.amoretech.memory.domain.timeline.model.StatusLocal
import com.amoretech.memory.domain.timeline.repository.FeedType
import com.amoretech.memory.domain.timeline.repository.HomeTimelineRepository
import org.koin.core.module.Module
import org.koin.dsl.module
import org.mobilenativefoundation.store.store5.Fetcher
import org.mobilenativefoundation.store.store5.SourceOfTruth
import org.mobilenativefoundation.store.store5.StoreBuilder

/**
 * Koin module containing all koin/bean definitions for
 * timeline repository delegates.
 */
val timelineRepoModule: Module =
    module {

        factory<HomeTimelineRepository> { RealHomeTimelineRepository(get()) }

        factory { get<TimelineDatabase>().asSourceOfTruth() }

        factory { get<MastodonApi>().timelineFetcher(authStorage = get()) }

        factory {
            val fetcher = get<Fetcher<FeedType, List<StatusEntity>>>()
            val sourceOfTruth = get<SourceOfTruth<FeedType, List<StatusEntity>, List<StatusLocal>>>()
            StoreBuilder
                .from(
                    fetcher = fetcher,
                    sourceOfTruth = sourceOfTruth,
                ).build()
        }
    }
