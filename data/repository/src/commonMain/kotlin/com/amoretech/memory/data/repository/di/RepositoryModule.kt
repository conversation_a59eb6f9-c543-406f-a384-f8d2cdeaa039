package com.amoretech.memory.data.repository.di

import com.amoretech.memory.data.repository.AuthenticationRepositoryImpl
import com.amoretech.memory.data.repository.StatusRepository
import com.amoretech.memory.data.repository.StatusRepositoryImpl
import com.amoretech.memory.data.repository.TimelineRepository
import com.amoretech.memory.data.repository.TimelineRepositoryImpl
import com.amoretech.memory.data.repository.login.repository.LoginRepositoryImpl
import com.amoretech.memory.data.repository.register.repository.RegistrationRepositoryImpl
import com.amoretech.memory.data.repository.serverlist.repository.FedIDBRepositoryImpl
import com.amoretech.memory.data.repository.usecase.BookmarkStatus
import com.amoretech.memory.data.repository.usecase.LikeStatus
import com.amoretech.memory.data.repository.usecase.PostStatus
import com.amoretech.memory.data.repository.usecase.ReblogStatus
import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository
import com.amoretech.memory.login.repository.LoginRepository
import com.amoretech.memory.register.repository.RegistrationRepository
import com.amoretech.memory.serverlist.repository.FedIDBRepository
import kotlinx.coroutines.Dispatchers
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin module containing all koin/bean definitions for
 * all repositories. Repositories encapsulate different data sources
 * and are typically injected into ViewModels or UseCases.
 */
val repositoryModule: Module =
    module {

        single<FedIDBRepository> {
            FedIDBRepositoryImpl(
                fedIDBApi = get(),
            )
        }

        single<RegistrationRepository> {
            RegistrationRepositoryImpl(
                mastodonApi = get(),
            )
        }

        single<LoginRepository> {
            LoginRepositoryImpl(
                mastodonApi = get(),
            )
        }

        single<AuthenticationRepository> {
            AuthenticationRepositoryImpl(
                mastodonApi = get(),
                applicationDao = get(),
                settings = get(),
                ioCoroutineContext = Dispatchers.Default,
            )
        }

        single<StatusRepository> {
            StatusRepositoryImpl(
                mastodonApi = get(),
                authRepository = get(),
            )
        }

        single<TimelineRepository> {
            TimelineRepositoryImpl(
                mastodonApi = get(),
                authRepository = get(),
            )
        }

        // Use cases
        factory {
            PostStatus(
                statusRepository = get(),
            )
        }

        factory {
            LikeStatus(
                statusRepository = get(),
            )
        }

        factory {
            ReblogStatus(
                statusRepository = get(),
            )
        }

        factory {
            BookmarkStatus(
                statusRepository = get(),
            )
        }
    }
