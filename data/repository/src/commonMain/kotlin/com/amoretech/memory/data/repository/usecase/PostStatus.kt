package com.amoretech.memory.data.repository.usecase

import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.data.repository.StatusRepository

class PostStatus(
    private val statusRepository: StatusRepository,
) {
    suspend operator fun invoke(
        content: String,
        inReplyToId: String? = null,
        sensitive: Boolean = false,
        spoilerText: String? = null,
        visibility: String = "public", // public, unlisted, private, direct
    ): Result<Status> =
        statusRepository.createStatus(
            content = content,
            inReplyToId = inReplyToId,
            sensitive = sensitive,
            spoilerText = spoilerText,
            visibility = visibility,
        )
}

class LikeStatus(
    private val statusRepository: StatusRepository,
) {
    suspend operator fun invoke(
        statusId: String,
        isLiked: Boolean,
    ): Result<Status> =
        if (isLiked) {
            statusRepository.favouriteStatus(statusId)
        } else {
            statusRepository.unfavouriteStatus(statusId)
        }
}

class ReblogStatus(
    private val statusRepository: StatusRepository,
) {
    suspend operator fun invoke(
        statusId: String,
        isReblogged: Boolean,
    ): Result<Status> =
        if (isReblogged) {
            statusRepository.reblogStatus(statusId)
        } else {
            statusRepository.unreblogStatus(statusId)
        }
}

class BookmarkStatus(
    private val statusRepository: StatusRepository,
) {
    suspend operator fun invoke(
        statusId: String,
        isBookmarked: Boolean,
    ): Result<Status> =
        if (isBookmarked) {
            statusRepository.bookmarkStatus(statusId)
        } else {
            statusRepository.unbookmarkStatus(statusId)
        }
}
