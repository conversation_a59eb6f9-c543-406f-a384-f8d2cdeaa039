package com.amoretech.memory.data.repository.timeline

import com.amoretech.memory.domain.timeline.model.StatusLocal
import com.amoretech.memory.domain.timeline.repository.FeedType
import com.amoretech.memory.domain.timeline.repository.HomeTimelineRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import org.mobilenativefoundation.store.store5.Store
import org.mobilenativefoundation.store.store5.StoreReadRequest
import org.mobilenativefoundation.store.store5.StoreReadResponse

// import org.mobilenativefoundation.store.store5.Store
// import org.mobilenativefoundation.store.store5.StoreRequest
// import org.mobilenativefoundation.store.store5.StoreResponse

class RealHomeTimelineRepository(
    private val store: Store<FeedType, List<StatusLocal>>,
) : HomeTimelineRepository {
    /**
     * returns a flow of home feed items from a database
     * anytime table rows are created/updated will return a new list of timeline items
     * on first return will also call network fetcher to get
     * latest from network and update local storage with it]
     */
    override fun read(
        feedType: FeedType,
        refresh: Boolean,
    ): Flow<StoreReadResponse<List<StatusLocal>>> =
        store
            .stream(StoreReadRequest.cached(key = feedType, refresh = true))
            .distinctUntilChanged()
}
