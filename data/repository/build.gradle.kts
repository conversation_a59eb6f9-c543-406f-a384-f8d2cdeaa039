import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.ksp)
    alias(libs.plugins.room)
    id("detekt")
    id("ktlint")
}

repositories {
    mavenCentral()
    google()
}

kotlin {

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.data.repository"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "repositoryKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(projects.kotlinUtils)
                implementation(projects.data.network)
                implementation(projects.data.persistence)
                implementation(projects.domain.authentication)
                implementation(projects.domain.serverlist)
                implementation(projects.domain.timeline)
                implementation(projects.domain.login)
                implementation(projects.domain.register)
                implementation(projects.fedidb)
                implementation(projects.mastodon)
                implementation(libs.kotlin.stdlib)
                implementation(libs.io.insert.koin.core)
                implementation(libs.kotlinx.coroutines.core)
                api(libs.store)
                implementation(libs.androidx.room.runtime)
                implementation(libs.kotlinx.datetime)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
                implementation(libs.org.jetbrains.kotlin.test.common)
                implementation(libs.org.jetbrains.kotlin.test.annotations.common)
                implementation(libs.org.jetbrains.kotlinx.coroutines.test)
                implementation(libs.app.cash.turbine)
            }
        }

        androidMain {
            dependencies {
                api(libs.org.jetbrains.kotlinx.atomicfu)
            }
        }

        iosMain {
            dependencies {}
        }

        jvmMain {
            dependencies {}
        }

        jvmTest {
            dependencies {
                implementation(kotlin("test-junit5"))
                // exclude junit4 to avoid conflict
                configurations.all {
                    exclude(group = "org.jetbrains.kotlin", module = "kotlin-test-junit")
                }
            }
        }
    }
}
