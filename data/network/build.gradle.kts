import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidKotlinMultiplatformLibrary)
    alias(libs.plugins.kotlin.serialization)
    id("detekt")
    id("ktlint")
}

kotlin {

    // to creates the 'desktopMain' and 'desktopTest' source sets
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
        testRuns["test"].executionTask.configure {
            useJUnitPlatform()
        }
    }

    androidLibrary {
        namespace = "com.amoretech.memory.data.network"
        compileSdk =
            libs.versions.android.compileSdk
                .get()
                .toInt()
        minSdk =
            libs.versions.android.minSdk
                .get()
                .toInt()
    }

    val xcfName = "networkKit"

    iosX64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    iosSimulatorArm64 {
        binaries.framework {
            baseName = xcfName
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(libs.kotlin.stdlib)
                implementation(projects.logging)
                implementation(projects.fedidb)
                implementation(projects.mastodon)
                implementation(libs.io.ktor.client.core)
                implementation(libs.io.ktor.client.cio)
                implementation(libs.io.ktor.client.serialization)
                implementation(libs.io.ktor.serialization.kotlinx.json)
                implementation(libs.io.ktor.client.content.negotiation)
                implementation(libs.io.ktor.client.auth)
                implementation(libs.io.ktor.client.logging)
                implementation(libs.kotlinx.serialization.json)
                implementation(libs.io.insert.koin.core)
            }
        }

        commonTest {
            dependencies {
                implementation(libs.kotlin.test)
                implementation(libs.io.ktor.client.mock)
                implementation(libs.org.jetbrains.kotlinx.coroutines.test)
                implementation(libs.io.insert.koin.test)
            }
        }

        androidMain {
            dependencies {
                implementation(libs.io.ktor.client.okhttp)
                implementation(libs.io.ktor.client.cio)
            }
        }

        androidUnitTest {
            dependencies {
                implementation(libs.io.ktor.client.mock.jvm)
            }
        }

        iosMain {
            dependencies {
                implementation(libs.io.ktor.client.darwin)
            }
        }

        jvmMain {
            dependencies {
                implementation(libs.io.ktor.client.cio)
            }
        }

        jvmTest {
            dependencies {
                implementation(kotlin("test-junit5"))
                // exclude junit4 to avoid conflict
                configurations.all {
                    exclude(group = "org.jetbrains.kotlin", module = "kotlin-test-junit")
                }
            }
        }
    }
}
