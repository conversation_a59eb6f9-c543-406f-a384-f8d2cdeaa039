package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/source/
 */
@Serializable
data class Source(
    // base attributes
    @SerialName("note") val note: String,
    @SerialName("fields") val fields: List<Field>,
    // optional attributes
    @SerialName("privacy") val privacy: Privacy,
    @SerialName("sensitive") val sensitive: <PERSON><PERSON><PERSON>,
    @SerialName("language") val language: String,
    @SerialName("follow_requests_count") val followRequestsCount: Int,
)

@Serializable
enum class Privacy {
    @SerialName("public")
    PUBLIC,

    @SerialName("private")
    PRIVATE,

    @SerialName("unlisted")
    UNLISTED,

    @SerialName("direct")
    DIRECT,
}
