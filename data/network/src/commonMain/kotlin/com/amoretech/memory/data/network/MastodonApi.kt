package com.amoretech.memory.data.network

import com.amoretech.memory.data.network.model.Application
import com.amoretech.memory.data.network.model.AvailableInstance
import com.amoretech.memory.data.network.model.Instance
import com.amoretech.memory.data.network.model.NewOauthApplication
import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.data.network.model.Token

interface MastodonApi {
    /**
     * List available an registered Instances which can be joined by the user
     *
     * @return Instances committed to the Mastodon Server Covenant and listed at joinmastodon.org
     */
    suspend fun listInstances(): Result<List<AvailableInstance>>

    /**
     * Register client applications that can be used to obtain OAuth tokens.
     *
     * @see https://docs.joinmastodon.org/methods/apps/#create-an-application
     *
     * @param clientName A name for your application
     * @param redirectUris Where the user should be redirected after authorization.
     * To display the authorization code to the user instead of redirecting to a web page,
     * use urn:ietf:wg:oauth:2.0:oob in this parameter.
     * @param scopes Space separated list of scopes. If none is provided, defaults to read.
     * @param website An optional URL to the homepage of your app
     *
     * @return Application with `client_id` and `client_secret`
     */
    suspend fun createApplication(
        domain: String,
        clientName: String,
        redirectUris: String,
        scopes: String,
        website: String?,
    ): Result<NewOauthApplication>

    /**
     * Obtain an access token that will authenticate our requests as the authorized user.
     * @see https://docs.joinmastodon.org/client/authorized/#token
     */
    suspend fun createAccessToken(
        domain: String,
        clientId: String,
        clientSecret: String,
        redirectUri: String,
        grantType: String,
        code: String,
        scope: String,
    ): Result<Token>

    /**
     * Confirm that the app’s OAuth2 credentials work.
     *
     * @see https://docs.joinmastodon.org/methods/apps/#verify_credentials
     *
     * @return an `application` if a valid token was provides in the authorization header
     */
    suspend fun verifyApplication(): Result<Application>

    /**
     * Fetch general information about the server
     *
     * @see https://docs.joinmastodon.org/methods/instance/
     *
     * @return an instance entity
     */
    suspend fun getInstance(domain: String? = null): Result<Instance>

    /**
     * Fetch home feed for a particular user
     * @see https://docs.joinmastodon.org/methods/timelines/#home
     * @param accessToken representing the user
     * @return a list of [Status]
     */
    suspend fun getHomeFeed(
        domain: String,
        accessToken: String,
    ): Result<List<Status>>
}
