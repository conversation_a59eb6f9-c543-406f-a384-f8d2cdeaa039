package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/poll/
 */
@Serializable
data class Poll(
    @SerialName("id") val id: String,
    @SerialName("expires_at") val expiresAt: String,
    @SerialName("expired") val expired: <PERSON><PERSON><PERSON>,
    @SerialName("multiple") val multiple: <PERSON><PERSON><PERSON>,
    @SerialName("votes_count") val votesCount: Int? = null,
    @SerialName("voters_count") val votersCount: Int? = null,
    @SerialName("voted") val voted: Boolean? = null,
    @SerialName("own_votes") val ownVotes: List<Int>? = null,
    @SerialName("options") val options: List<PollHash>? = null,
    @SerialName("emojis") val emojis: List<Emoji>? = null,
)

@Serializable
data class PollHash(
    val title: String,
    @SerialName("votes_count") val votesCount: Int,
)
