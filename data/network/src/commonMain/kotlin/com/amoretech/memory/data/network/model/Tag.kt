package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/tag/
 */
@Serializable
data class Tag(
    // required attributes
    @SerialName("name") val name: String,
    @SerialName("url") val url: String,
    // optional attributes
    @SerialName("history") val history: List<History>? = emptyList(),
)
