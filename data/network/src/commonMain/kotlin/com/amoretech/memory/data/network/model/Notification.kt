package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/notification/
 */
@Serializable
data class Notification(
    // required attributes
    @SerialName("id") val id: String,
    @SerialName("type") val type: NotificationType,
    @SerialName("created_at") val createdAt: String,
    @SerialName("account") val account: Account,
    // optional attributes
    @SerialName("status") val status: Status,
)

@Serializable
enum class NotificationType {
    @SerialName("follow")
    FOLLOW,

    @SerialName("follow_request")
    FOLLOW_REQUEST,

    @SerialName("mention")
    MENTION,

    @SerialName("reblog")
    REBLOG,

    @SerialName("favourite")
    FAVOURITE,

    @SerialName("poll")
    POLL,

    @SerialName("status")
    STATUS,
}
