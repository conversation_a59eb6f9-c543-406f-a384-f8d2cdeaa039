package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/application/
 */
@Serializable
data class Application(
    val name: String,
    @SerialName("vapid_key") val vapidKey: String? = null,
    // optional attributes
    val website: String? = null,
)

@Serializable
data class NewOauthApplication(
    val id: String,
    val name: String,
    @SerialName("vapid_key") val vapidKey: String,
    // client attributes
    @SerialName("client_id") val clientId: String,
    @SerialName("client_secret") val clientSecret: String,
    // optional attributes
    val website: String? = null,
)
