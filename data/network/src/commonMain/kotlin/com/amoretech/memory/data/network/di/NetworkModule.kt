package com.amoretech.memory.data.network.di

import com.amoretech.memory.data.network.MastodonApi
import com.amoretech.memory.data.network.MastodonApiKtor
import com.amoretech.memory.fedidb.api.FedIDBApi
import com.amoretech.memory.logging.MemoryLogger
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.http.URLProtocol
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin module containing all koin/bean definitions for
 * network/api related classes
 */
val networkModule: Module =
    module {
        single {
            HttpClient(CIO) {
                install(Logging) {
                    logger =
                        object : Logger {
                            override fun log(message: String) {
                                MemoryLogger.v(message, null, "HttpClient")
                            }
                        }
                    level = LogLevel.BODY
                }
                // install plugin so we can use type-safe data models for serialization in ktor
                install(ContentNegotiation) {
                    json(
                        Json {
                            prettyPrint = true
                            ignoreUnknownKeys = true
                        },
                    )
                }
                defaultRequest {
                    url {
                        protocol = URLProtocol.HTTPS
                    }
                }
            }
        }

        single<MastodonApi> {
            MastodonApiKtor(
                httpClient = get(),
            )
        }

        single<FedIDBApi> {
            FedIDBApi(
                httpClient = get(),
            )
        }

        single<com.amoretech.memory.mastodon.api.MastodonApi> {
            com.amoretech.memory.mastodon.api.MastodonApi(
                httpClient = get(),
            )
        }
    }
