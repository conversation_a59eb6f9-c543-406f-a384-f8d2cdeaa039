package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/pushsubscription/
 */
@Serializable
data class PushSubscription(
    // required attributes
    @SerialName("id") val id: String,
    @SerialName("endpoint") val endpoint: String,
    @SerialName("server_key") val serverKey: String,
    @SerialName("alerts") val alerts: Alerts,
)

@Serializable
data class Alerts(
    val follow: <PERSON><PERSON><PERSON>,
    val favourite: <PERSON><PERSON><PERSON>,
    val mention: <PERSON><PERSON><PERSON>,
    val reblog: <PERSON><PERSON>an,
    val poll: <PERSON><PERSON><PERSON>,
)
