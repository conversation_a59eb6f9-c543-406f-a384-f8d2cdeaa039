package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/card/
 */
@Serializable
data class Card(
    // base attributes
    @SerialName("url") val url: String,
    @SerialName("title") val title: String,
    @SerialName("description") val description: String,
    @SerialName("type") val type: CardType,
    // optional attributes
    @SerialName("author_name") val authorName: String? = null,
    @SerialName("author_url") val authorUrl: String? = null,
    @SerialName("provider_name") val providerName: String? = null,
    @SerialName("provider_url") val providerUrl: String? = null,
    @SerialName("html") val html: String? = null,
    @SerialName("width") val width: Int? = null,
    @SerialName("height") val height: Int? = null,
    @SerialName("image") val image: String? = null,
    @SerialName("embed_url") val embedUrl: String? = null,
    @SerialName("blurhash") val blurhash: String? = null,
)

@Serializable
enum class CardType {
    @SerialName("link")
    LINK,

    @SerialName("photo")
    PHOTO,

    @SerialName("video")
    VIDEO,

    @SerialName("rich")
    RICH,
}
