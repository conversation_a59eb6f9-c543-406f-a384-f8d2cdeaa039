package com.amoretech.memory.data.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.joinmastodon.org/entities/preferences/
 */
@Serializable
data class Preferences(
    // required attributes
    @SerialName("posting:default:visibility") val postingVisibility: PostingVisibility,
    @SerialName("posting:default:sensitive") val postingSensitive: <PERSON><PERSON><PERSON>,
    @SerialName("posting:default:language") val postingLanguage: String? = null,
    @SerialName("reading:expand:media") val readingMedia: ReadingMedia,
    @SerialName("reading:expand:spoilers") val readingSpoilers: <PERSON><PERSON><PERSON>,
    // optional attributes
    @SerialName("history") val history: List<History>? = null,
)

@Serializable
enum class PostingVisibility {
    @SerialName("public")
    PUBLIC,

    @SerialName("unlisted")
    UNLISTED,

    @SerialName("private")
    PRIVATE,

    @SerialName("direct")
    DIRECT,
}

@Serializable
enum class ReadingMedia {
    @SerialName("default")
    DEFAULT,

    @SerialName("show_all")
    SHOW_ALL,

    @SerialName("hide_all")
    HIDE_ALL,
}
