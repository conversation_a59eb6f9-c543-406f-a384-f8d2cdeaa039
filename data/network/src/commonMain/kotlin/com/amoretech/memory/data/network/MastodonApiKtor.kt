package com.amoretech.memory.data.network

import com.amoretech.memory.data.network.model.Application
import com.amoretech.memory.data.network.model.AvailableInstance
import com.amoretech.memory.data.network.model.Instance
import com.amoretech.memory.data.network.model.NewOauthApplication
import com.amoretech.memory.data.network.model.Status
import com.amoretech.memory.data.network.model.Token
import com.amoretech.memory.data.network.model.request.CreateAccessTokenBody
import com.amoretech.memory.data.network.model.request.CreateApplicationBody
import com.amoretech.memory.data.network.util.runCatchingIgnoreCancelled
import com.amoretech.memory.logging.MemoryLogger
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.get
import io.ktor.client.request.headers
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import io.ktor.http.path
import kotlinx.serialization.SerializationException

internal class MastodonApiKtor(
    private val httpClient: HttpClient,
) : MastodonApi {
    override suspend fun listInstances(): Result<List<AvailableInstance>> =
        runCatchingIgnoreCancelled {
            httpClient.get("https://api.joinmastodon.org/servers").body()
        }

    override suspend fun createAccessToken(
        domain: String,
        clientId: String,
        clientSecret: String,
        redirectUri: String,
        grantType: String,
        code: String,
        scope: String,
    ): Result<Token> {
        MemoryLogger.d {
            "Auth called!"
        }
        return runCatchingIgnoreCancelled<Token> {
            httpClient
                .post {
                    url {
                        host = domain
                        path("/oauth/token")
                    }
                    contentType(ContentType.Application.Json)
                    setBody(
                        CreateAccessTokenBody(
                            scope = scope,
                            code = code,
                            clientId = clientId,
                            clientSecret = clientSecret,
                            redirectUri = redirectUri,
                            grantType = grantType,
                        ),
                    )
                }.body()
        }.onFailure { t ->
            MemoryLogger.w(throwable = t) {
                "Error when creating access token"
            }
        }
    }

    override suspend fun createApplication(
        domain: String,
        clientName: String,
        redirectUris: String,
        scopes: String,
        website: String?,
    ): Result<NewOauthApplication> =
        runCatchingIgnoreCancelled<NewOauthApplication> {
            httpClient
                .post {
                    url {
                        host = domain
                        path("/api/v1/apps")
                    }
                    contentType(ContentType.Application.Json)
                    setBody(
                        CreateApplicationBody(
                            scopes = scopes,
                            clientName = clientName,
                            redirectUris = redirectUris,
                        ),
                    )
                }.body()
        }.onFailure { t ->
            MemoryLogger.w(throwable = t) {
                "Error when creating application."
            }
        }

    override suspend fun verifyApplication(): Result<Application> =
        runCatchingIgnoreCancelled<Application> {
            httpClient
                .get("/api/v1/apps/verify_credentials") {
                    headers {
                        append(HttpHeaders.Authorization, "testAuthorization")
                    }
                }.body()
        }

    override suspend fun getInstance(domain: String?): Result<Instance> =
        try {
            Result.success(
                httpClient
                    .get("/api/v1/instance") {
                        domain?.let {
                            headers.append("domain", domain)
                        }
                    }.body(),
            )
        } catch (exception: SerializationException) {
            Result.failure(exception = exception)
        } catch (exception: ResponseException) {
            Result.failure(exception = exception)
        }

    override suspend fun getHomeFeed(
        domain: String,
        accessToken: String,
    ): Result<List<Status>> {
        MemoryLogger.d { "==== Berarier token is $accessToken" }
        return try {
            val response =
                httpClient.get("https://$domain/api/v1/timelines/home") {
                    headers {
                        append(HttpHeaders.Authorization, "Bearer $accessToken")
                    }
                }

            val statuses = response.body<List<Status>>()
            Result.success(statuses)
        } catch (e: Exception) {
            Result.failure(e)
        }
//        return runCatchingIgnoreCancelled<List<Status>> {
//            httpClient
//                .get {
//                    url {
//                        host = domain
//                        path("/api/v1/timelines/home")
//                    }
//                    headers {
//                        append(HttpHeaders.Authorization, "Bearer $accessToken")
//                    }
//                }.body()
//        }
    }
}
