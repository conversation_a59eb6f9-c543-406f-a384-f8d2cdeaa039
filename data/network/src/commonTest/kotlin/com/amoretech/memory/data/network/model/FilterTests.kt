package com.amoretech.memory.data.network.model

import kotlinx.serialization.json.Json
import kotlin.test.Test
import kotlin.test.assertEquals

class FilterTests {
    @Test
    fun `deserialize required fields should succeed`() {
        // given
        val json =
            """
            {
                "id": "8449",
                "phrase": "test",
                "context":
                [
                    "home",
                    "notifications",
                    "public",
                    "thread"
                ],
                "whole_word": false,
                "expires_at": "2019-11-26T09:08:06.254Z",
                "irreversible": true
            }
            """.trimIndent()

        // when
        val filter = Json.decodeFromString<Filter>(json)

        // then
        assertEquals(expected = "8449", actual = filter.id)
        assertEquals(expected = "test", actual = filter.phrase)
        assertEquals(
            expected =
                listOf(
                    FilterContext.HOME,
                    FilterContext.NOTIFICATIONS,
                    FilterContext.PUBLIC,
                    FilterContext.THREAD,
                ),
            actual = filter.context,
        )
        assertEquals(expected = false, actual = filter.wholeWord)
        assertEquals(expected = "2019-11-26T09:08:06.254Z", actual = filter.expiresAt)
        assertEquals(expected = true, actual = filter.irreversible)
    }
}
