package com.amoretech.memory.data.network.model

import kotlinx.serialization.json.Json
import kotlin.test.Ignore
import kotlin.test.Test
import kotlin.test.assertEquals

class AccountTests {
    // TODO: fix loading json from resources
    @Ignore
    @Test
    fun `deserialize required fields should succeed`() {
        // given
        // val json: String = javaClass.classLoader.getResource("response_account_required.json").readText()
        val json: String = ""

        // when
        val account = Json.decodeFromString<Account>(json)

        // then
        assertEquals(expected = "23634", actual = account.id)
    }
}
