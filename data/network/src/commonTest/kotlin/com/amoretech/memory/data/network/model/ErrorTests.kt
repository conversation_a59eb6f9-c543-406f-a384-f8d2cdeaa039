package com.amoretech.memory.data.network.model

// import kotlinx.serialization.json.Json
// import kotlin.test.Test
// import kotlin.test.assertEquals
//
// class ErrorTests {
//    @Test
//    fun `deserialize video card should succeed`() {
//        // given
//        val json = """
//        {
//            "error": "invalid_grant",
//            "error_description": "The provided authorization grant is invalid, expired, revoked, does not match the redirection URI used in the authorization request, or was issued to another client."
//        }
//        """.trimIndent()
//
//        // when
//        val error = Json.decodeFromString<Error>(json)
//
//        // then
//        assertEquals(expected = "invalid_grant", actual = error.message)
//        assertEquals(
//            expected = "The provided authorization grant is invalid, expired, " +
//                "revoked, does not match the redirection URI used " +
//                "in the authorization request, or was issued to another client.",
//            actual = error.message
//        )
//    }
// }
