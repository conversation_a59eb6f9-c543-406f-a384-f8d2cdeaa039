@file:Suppress("MaximumLineLength", "MaxLineLength")

package com.amoretech.memory.data.network.fixtures

val homeFeed =
    """
       [
	{
		"id": "109518540195637455",
		"created_at": "2022-12-15T16:05:00.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://mastodon.social/users/caseynewton/statuses/109518539992018383",
		"url": "https://mastodon.social/@caseynewton/109518539992018383",
		"replies_count": 9,
		"reblogs_count": 1,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>Is there a good desktop Mastodon client for Mac that will let me 'pin the timeline to the top' so I can watch toots stream down all day like I used to with Twitter?</p>",
		"filtered": [],
		"reblog": null,
		"account": {
			"id": "109305876702749697",
			"username": "caseynewton",
			"acct": "<EMAIL>",
			"display_name": "Casey Newton",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2017-04-03T00:00:00.000Z",
			"note": "<p>Email salesman at Platformer and podcast co-host at Hard Fork.</p>",
			"url": "https://mastodon.social/@caseynewton",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/305/876/702/749/697/original/8a29e7bd52e49c59.png",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/305/876/702/749/697/original/8a29e7bd52e49c59.png",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/305/876/702/749/697/original/d84511f154d21b4b.jpg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/305/876/702/749/697/original/d84511f154d21b4b.jpg",
			"followers_count": 10268,
			"following_count": 144,
			"statuses_count": 62,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": []
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518455001667429",
		"created_at": "2022-12-15T15:43:23.321Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/zachklipp/statuses/109518455001667429/activity",
		"url": "https://androiddev.social/users/zachklipp/statuses/109518455001667429/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109515255677164600",
			"created_at": "2022-12-15T02:09:45.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://fosstodon.org/users/ids1024/statuses/109515255660791632",
			"url": "https://fosstodon.org/@ids1024/109515255660791632",
			"replies_count": 8,
			"reblogs_count": 76,
			"favourites_count": 4,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>Using <a href=\"https://fosstodon.org/tags/vim\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>vim</span></a> is easy once you learn a few basic keybindings.</p><p>h and l - move left and right<br>j and k - move down and up<br>η and λ - move backwards and forwards through time<br>ξ and κ - translation through additional temporal dimension (if applicable)<br>ᚻ, ᛄ, ᚳ and ᛚ - moving left, down, up, and right through celestial spheres<br>𐤄 and 𐤋 - switch deity to pantheon member to left or right<br>ᛄ - supplicate to chosen deity<br>ᚳ - challenge chosen deity (dangerous)<br>:q - exit</p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109439855539266132",
				"username": "ids1024",
				"acct": "<EMAIL>",
				"display_name": "Ian Douglas Scott",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2022-11-23T00:00:00.000Z",
				"note": "<p>Compiler of compilers.</p>",
				"url": "https://fosstodon.org/@ids1024",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/439/855/539/266/132/original/93abae9801e99ec9.png",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/439/855/539/266/132/original/93abae9801e99ec9.png",
				"header": "https://androiddev.social/headers/original/missing.png",
				"header_static": "https://androiddev.social/headers/original/missing.png",
				"followers_count": 18,
				"following_count": 101,
				"statuses_count": 40,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "Github",
						"value": "<a href=\"https://github.com/ids1024\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">github.com/ids1024</span><span class=\"invisible\"></span></a>",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [
				{
					"name": "vim",
					"url": "https://androiddev.social/tags/vim"
				}
			],
			"emojis": [],
			"card": null,
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109275230741524764",
			"username": "zachklipp",
			"acct": "zachklipp",
			"display_name": "Zach Klipp (he/him)🌻",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-02T00:00:00.000Z",
			"note": "<p>Engineer working on Android &amp; Jetpack Compose @ Google, but opinions are my own. Formerly Square, Amazon.</p><p><a href=\"https://androiddev.social/tags/StopAsianHate\" class=\"mention hashtag\" rel=\"tag\">#<span>StopAsianHate</span></a> <a href=\"https://androiddev.social/tags/BLM\" class=\"mention hashtag\" rel=\"tag\">#<span>BLM</span></a> <a href=\"https://androiddev.social/tags/ACAB\" class=\"mention hashtag\" rel=\"tag\">#<span>ACAB</span></a></p><p><a href=\"https://androiddev.social/tags/Android\" class=\"mention hashtag\" rel=\"tag\">#<span>Android</span></a> <a href=\"https://androiddev.social/tags/AndroidDev\" class=\"mention hashtag\" rel=\"tag\">#<span>AndroidDev</span></a> <a href=\"https://androiddev.social/tags/Kotlin\" class=\"mention hashtag\" rel=\"tag\">#<span>Kotlin</span></a> <a href=\"https://androiddev.social/tags/JetpackCompose\" class=\"mention hashtag\" rel=\"tag\">#<span>JetpackCompose</span></a></p>",
			"url": "https://androiddev.social/@zachklipp",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/275/230/741/524/764/original/0d99f7672672fe66.jpeg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/275/230/741/524/764/original/0d99f7672672fe66.jpeg",
			"header": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/275/230/741/524/764/original/80ed812fe9fab016.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/275/230/741/524/764/original/80ed812fe9fab016.jpeg",
			"followers_count": 1321,
			"following_count": 325,
			"statuses_count": 1204,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "Blog",
					"value": "<a href=\"https://dev.to/zachklipp\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">dev.to/zachklipp</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-15T23:41:20.469+00:00"
				},
				{
					"name": "GitHub",
					"value": "<a href=\"https://github.com/zach-klippenstein\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">github.com/zach-klippenstein</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-15T23:41:21.051+00:00"
				},
				{
					"name": "Website",
					"value": "<a href=\"http://www.zachklipp.com/\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">http://www.</span><span class=\"\">zachklipp.com/</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-04T15:31:46.178+00:00"
				},
				{
					"name": "Location",
					"value": "San Francisco, CA",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518429334530487",
		"created_at": "2022-12-15T15:36:51.671Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/zachklipp/statuses/109518429334530487/activity",
		"url": "https://androiddev.social/users/zachklipp/statuses/109518429334530487/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109518409983578086",
			"created_at": "2022-12-15T15:31:44.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://hachyderm.io/users/shanselman/statuses/109518409218018946",
			"url": "https://hachyderm.io/@shanselman/109518409218018946",
			"replies_count": 3,
			"reblogs_count": 12,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>US based folks - free Covid tests just started today again. Each address can get FOUR FREE <a href=\"https://www.covid.gov/tests\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://www.</span><span class=\"\">covid.gov/tests</span><span class=\"invisible\"></span></a></p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109378871970418249",
				"username": "shanselman",
				"acct": "<EMAIL>",
				"display_name": "Scott Hanselman :verified:👸🏽🐝🌮",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2022-11-20T00:00:00.000Z",
				"note": "<p>Code, OSS, STEM, Beyoncé, <a href=\"https://hachyderm.io/tags/T1D\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>T1D</span></a>, Hanselminutes inclusive tech podcast! MSFT Developer Division Community <a href=\"https://hachyderm.io/tags/DevRel\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>DevRel</span></a>🐹🌮YouTube+TikTok My opinions <a href=\"https://hanselman.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">hanselman.com</span><span class=\"invisible\"></span></a></p>",
				"url": "https://hachyderm.io/@shanselman",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/378/871/970/418/249/original/b4fabccafae3c151.png",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/378/871/970/418/249/original/b4fabccafae3c151.png",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/378/871/970/418/249/original/04d152efd3d470f1.jpg",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/378/871/970/418/249/original/04d152efd3d470f1.jpg",
				"followers_count": 23818,
				"following_count": 1555,
				"statuses_count": 806,
				"last_status_at": "2022-12-15",
				"emojis": [
					{
						"shortcode": "verified",
						"url": "https://cdn.masto.host/androiddevsocial/cache/custom_emojis/images/000/000/873/original/1b5b55f5f2ed3752.png",
						"static_url": "https://cdn.masto.host/androiddevsocial/cache/custom_emojis/images/000/000/873/static/1b5b55f5f2ed3752.png",
						"visible_in_picker": true
					}
				],
				"fields": [
					{
						"name": "Website",
						"value": "<a href=\"https://hanselman.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">hanselman.com</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-12-14T08:17:07.089+00:00"
					},
					{
						"name": "Podcast",
						"value": "<a href=\"https://hanselminutes.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">hanselminutes.com</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-12-14T08:17:07.949+00:00"
					},
					{
						"name": "GitHub",
						"value": "<a href=\"https://github.com/shanselman\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">github.com/shanselman</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-12-14T08:17:08.573+00:00"
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": {
				"url": "https://www.covid.gov/tests",
				"title": "COVID.gov/tests - Free at-home COVID-19 tests",
				"description": "Every U.S. household is eligible to order 4 free at-home COVID-⁠19 tests.",
				"type": "link",
				"author_name": "HHS",
				"author_url": "",
				"provider_name": "COVID.gov",
				"provider_url": "",
				"html": "",
				"width": 400,
				"height": 213,
				"image": "https://cdn.masto.host/androiddevsocial/cache/preview_cards/images/000/207/585/original/6a473c23a3aff2a0.jpg",
				"embed_url": "",
				"blurhash": "UhL4NgDi00E1E1M{o#xuM{jZozt7WBkCs:ni"
			},
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109275230741524764",
			"username": "zachklipp",
			"acct": "zachklipp",
			"display_name": "Zach Klipp (he/him)🌻",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-02T00:00:00.000Z",
			"note": "<p>Engineer working on Android &amp; Jetpack Compose @ Google, but opinions are my own. Formerly Square, Amazon.</p><p><a href=\"https://androiddev.social/tags/StopAsianHate\" class=\"mention hashtag\" rel=\"tag\">#<span>StopAsianHate</span></a> <a href=\"https://androiddev.social/tags/BLM\" class=\"mention hashtag\" rel=\"tag\">#<span>BLM</span></a> <a href=\"https://androiddev.social/tags/ACAB\" class=\"mention hashtag\" rel=\"tag\">#<span>ACAB</span></a></p><p><a href=\"https://androiddev.social/tags/Android\" class=\"mention hashtag\" rel=\"tag\">#<span>Android</span></a> <a href=\"https://androiddev.social/tags/AndroidDev\" class=\"mention hashtag\" rel=\"tag\">#<span>AndroidDev</span></a> <a href=\"https://androiddev.social/tags/Kotlin\" class=\"mention hashtag\" rel=\"tag\">#<span>Kotlin</span></a> <a href=\"https://androiddev.social/tags/JetpackCompose\" class=\"mention hashtag\" rel=\"tag\">#<span>JetpackCompose</span></a></p>",
			"url": "https://androiddev.social/@zachklipp",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/275/230/741/524/764/original/0d99f7672672fe66.jpeg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/275/230/741/524/764/original/0d99f7672672fe66.jpeg",
			"header": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/275/230/741/524/764/original/80ed812fe9fab016.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/275/230/741/524/764/original/80ed812fe9fab016.jpeg",
			"followers_count": 1321,
			"following_count": 325,
			"statuses_count": 1204,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "Blog",
					"value": "<a href=\"https://dev.to/zachklipp\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">dev.to/zachklipp</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-15T23:41:20.469+00:00"
				},
				{
					"name": "GitHub",
					"value": "<a href=\"https://github.com/zach-klippenstein\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">github.com/zach-klippenstein</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-15T23:41:21.051+00:00"
				},
				{
					"name": "Website",
					"value": "<a href=\"http://www.zachklipp.com/\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">http://www.</span><span class=\"\">zachklipp.com/</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-04T15:31:46.178+00:00"
				},
				{
					"name": "Location",
					"value": "San Francisco, CA",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518421979144592",
		"created_at": "2022-12-15T15:34:59.437Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/zachklipp/statuses/109518421979144592/activity",
		"url": "https://androiddev.social/users/zachklipp/statuses/109518421979144592/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109516134979876793",
			"created_at": "2022-12-15T05:53:20.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://hachyderm.io/users/dfeldman/statuses/109516134813975737",
			"url": "https://hachyderm.io/@dfeldman/109516134813975737",
			"replies_count": 44,
			"reblogs_count": 52,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>Fun fact: the MP3 file format is as old today as 8-track tapes were when MP3 was invented</p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109344795589274997",
				"username": "dfeldman",
				"acct": "<EMAIL>",
				"display_name": "Daniel Feldm :verified: n",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2022-11-14T00:00:00.000Z",
				"note": "<p>I’m a software engineer at a big tech company. I work on SPIFFE, an open source cloud security tool. I’m mostly here to toot about cloud security, science, AI art, 3D printing, computer trivia, and life in Minneapolis.</p><p>He/him</p>",
				"url": "https://hachyderm.io/@dfeldman",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/344/795/589/274/997/original/04a8bff4a9d16951.jpeg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/344/795/589/274/997/original/04a8bff4a9d16951.jpeg",
				"header": "https://androiddev.social/headers/original/missing.png",
				"header_static": "https://androiddev.social/headers/original/missing.png",
				"followers_count": 1533,
				"following_count": 1727,
				"statuses_count": 732,
				"last_status_at": "2022-12-15",
				"emojis": [
					{
						"shortcode": "verified",
						"url": "https://cdn.masto.host/androiddevsocial/cache/custom_emojis/images/000/000/873/original/1b5b55f5f2ed3752.png",
						"static_url": "https://cdn.masto.host/androiddevsocial/cache/custom_emojis/images/000/000/873/static/1b5b55f5f2ed3752.png",
						"visible_in_picker": true
					}
				],
				"fields": [
					{
						"name": "Twitter",
						"value": "Twitter.com/d_feldman",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": null,
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109275230741524764",
			"username": "zachklipp",
			"acct": "zachklipp",
			"display_name": "Zach Klipp (he/him)🌻",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-02T00:00:00.000Z",
			"note": "<p>Engineer working on Android &amp; Jetpack Compose @ Google, but opinions are my own. Formerly Square, Amazon.</p><p><a href=\"https://androiddev.social/tags/StopAsianHate\" class=\"mention hashtag\" rel=\"tag\">#<span>StopAsianHate</span></a> <a href=\"https://androiddev.social/tags/BLM\" class=\"mention hashtag\" rel=\"tag\">#<span>BLM</span></a> <a href=\"https://androiddev.social/tags/ACAB\" class=\"mention hashtag\" rel=\"tag\">#<span>ACAB</span></a></p><p><a href=\"https://androiddev.social/tags/Android\" class=\"mention hashtag\" rel=\"tag\">#<span>Android</span></a> <a href=\"https://androiddev.social/tags/AndroidDev\" class=\"mention hashtag\" rel=\"tag\">#<span>AndroidDev</span></a> <a href=\"https://androiddev.social/tags/Kotlin\" class=\"mention hashtag\" rel=\"tag\">#<span>Kotlin</span></a> <a href=\"https://androiddev.social/tags/JetpackCompose\" class=\"mention hashtag\" rel=\"tag\">#<span>JetpackCompose</span></a></p>",
			"url": "https://androiddev.social/@zachklipp",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/275/230/741/524/764/original/0d99f7672672fe66.jpeg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/275/230/741/524/764/original/0d99f7672672fe66.jpeg",
			"header": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/275/230/741/524/764/original/80ed812fe9fab016.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/275/230/741/524/764/original/80ed812fe9fab016.jpeg",
			"followers_count": 1321,
			"following_count": 325,
			"statuses_count": 1204,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "Blog",
					"value": "<a href=\"https://dev.to/zachklipp\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">dev.to/zachklipp</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-15T23:41:20.469+00:00"
				},
				{
					"name": "GitHub",
					"value": "<a href=\"https://github.com/zach-klippenstein\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">github.com/zach-klippenstein</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-15T23:41:21.051+00:00"
				},
				{
					"name": "Website",
					"value": "<a href=\"http://www.zachklipp.com/\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">http://www.</span><span class=\"\">zachklipp.com/</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-04T15:31:46.178+00:00"
				},
				{
					"name": "Location",
					"value": "San Francisco, CA",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518381026146507",
		"created_at": "2022-12-15T15:23:05.000Z",
		"in_reply_to_id": "109518376787333334",
		"in_reply_to_account_id": "109274267286908118",
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://toot.thoughtworks.com/users/mfowler/statuses/109518375176392680",
		"url": "https://toot.thoughtworks.com/@mfowler/109518375176392680",
		"replies_count": 1,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>I say that knowing I'm fortunate to not be one of those who suffer real harm from internet thugs due to that simplistic ideology</p>",
		"filtered": [],
		"reblog": null,
		"account": {
			"id": "109274267286908118",
			"username": "mfowler",
			"acct": "<EMAIL>",
			"display_name": "Martin Fowler",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-04-25T00:00:00.000Z",
			"note": "<p>Author and loudmouth on software development. Works at Thoughtworks.  Also hikes, watches theater, and plays modern board games.</p>",
			"url": "https://toot.thoughtworks.com/@mfowler",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/267/286/908/118/original/da3fd245793af66e.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/267/286/908/118/original/da3fd245793af66e.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/267/286/908/118/original/40758799f029b7eb.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/267/286/908/118/original/40758799f029b7eb.jpeg",
			"followers_count": 10106,
			"following_count": 79,
			"statuses_count": 151,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": [
				{
					"name": "Website",
					"value": "<a href=\"https://martinfowler.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">martinfowler.com</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-06T20:47:24.044+00:00"
				},
				{
					"name": "Pronouns",
					"value": "he/him",
					"verified_at": null
				},
				{
					"name": "email",
					"value": "<EMAIL>",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518376787333334",
		"created_at": "2022-12-15T15:23:04.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://toot.thoughtworks.com/users/mfowler/statuses/109518375128337637",
		"url": "https://toot.thoughtworks.com/@mfowler/109518375128337637",
		"replies_count": 4,
		"reblogs_count": 2,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>I'm glad to note that even after the change of ownership of Twitter, I can still indulge in the amusement of watching a simplistic ideology (free-speech absolutism) collapse under its own weight when faced with reality</p>",
		"filtered": [],
		"reblog": null,
		"account": {
			"id": "109274267286908118",
			"username": "mfowler",
			"acct": "<EMAIL>",
			"display_name": "Martin Fowler",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-04-25T00:00:00.000Z",
			"note": "<p>Author and loudmouth on software development. Works at Thoughtworks.  Also hikes, watches theater, and plays modern board games.</p>",
			"url": "https://toot.thoughtworks.com/@mfowler",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/267/286/908/118/original/da3fd245793af66e.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/267/286/908/118/original/da3fd245793af66e.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/267/286/908/118/original/40758799f029b7eb.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/267/286/908/118/original/40758799f029b7eb.jpeg",
			"followers_count": 10106,
			"following_count": 79,
			"statuses_count": 151,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": [
				{
					"name": "Website",
					"value": "<a href=\"https://martinfowler.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">martinfowler.com</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-06T20:47:24.044+00:00"
				},
				{
					"name": "Pronouns",
					"value": "he/him",
					"verified_at": null
				},
				{
					"name": "email",
					"value": "<EMAIL>",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518367474303996",
		"created_at": "2022-12-15T15:21:07.759Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/sepdroid/statuses/109518367474303996/activity",
		"url": "https://androiddev.social/users/sepdroid/statuses/109518367474303996/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109515140011524901",
			"created_at": "2022-12-15T01:40:19.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://mastodon.social/users/scalzi/statuses/109515139953245731",
			"url": "https://mastodon.social/@scalzi/109515139953245731",
			"replies_count": 278,
			"reblogs_count": 4,
			"favourites_count": 2,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>I have fewer followers on Mastodon than on Twitter by an order of magnitude, but I'm guessing the number of followers here who are bots/trolls is much closer to zero than over there. Hello, real humans!</p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109275971660663198",
				"username": "scalzi",
				"acct": "<EMAIL>",
				"display_name": "Scalzi",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2017-11-05T00:00:00.000Z",
				"note": "<p>I enjoy pie.</p>",
				"url": "https://mastodon.social/@scalzi",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/275/971/660/663/198/original/c9a3713c8f2c0df0.jpg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/275/971/660/663/198/original/c9a3713c8f2c0df0.jpg",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/275/971/660/663/198/original/65707a3677d4a873.jpg",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/275/971/660/663/198/original/65707a3677d4a873.jpg",
				"followers_count": 13167,
				"following_count": 72,
				"statuses_count": 165,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "Verification",
						"value": "<a href=\"https://whatever.scalzi.com/2022/11/22/this-is-a-post-to-set-up-verification-on-mastodon-you-can-totally-ignore-it/\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">whatever.scalzi.com/2022/11/22</span><span class=\"invisible\">/this-is-a-post-to-set-up-verification-on-mastodon-you-can-totally-ignore-it/</span></a>",
						"verified_at": "2022-12-12T19:41:03.318+00:00"
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": null,
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109279987650139218",
			"username": "sepdroid",
			"acct": "sepdroid",
			"display_name": "Sepideh",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>Android Dev with an interest in attending local meetings about K-12 education</p>",
			"url": "https://androiddev.social/@sepdroid",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"header": "https://androiddev.social/headers/original/missing.png",
			"header_static": "https://androiddev.social/headers/original/missing.png",
			"followers_count": 144,
			"following_count": 305,
			"statuses_count": 355,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "pronouns",
					"value": "she/her",
					"verified_at": null
				},
				{
					"name": "pronounced",
					"value": "Sa-pita",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518363900583915",
		"created_at": "2022-12-15T15:20:13.230Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/sepdroid/statuses/109518363900583915/activity",
		"url": "https://androiddev.social/users/sepdroid/statuses/109518363900583915/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109518295192756833",
			"created_at": "2022-12-15T15:02:44.834Z",
			"in_reply_to_id": "109518285473387967",
			"in_reply_to_account_id": "109384856559579254",
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://androiddev.social/users/sepdroid/statuses/109518295192756833",
			"url": "https://androiddev.social/@sepdroid/109518295192756833",
			"replies_count": 0,
			"reblogs_count": 3,
			"favourites_count": 4,
			"edited_at": "2022-12-15T15:03:00.982Z",
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p><span class=\"h-card\"><a href=\"https://mastodon.online/@BenCisco\" class=\"u-url mention\">@<span>BenCisco</span></a></span> I grew up very close to Louisiana listening to a whole lot of Aaron Neville and zydeco, and I had never heard that Christmas song.</p><p><a href=\"https://www.youtube.com/watch?v=WjuIcvQy8i4\" target=\"_blank\" rel=\"nofollow noopener noreferrer\"><span class=\"invisible\">https://www.</span><span class=\"ellipsis\">youtube.com/watch?v=WjuIcvQy8i</span><span class=\"invisible\">4</span></a></p>",
			"filtered": [],
			"reblog": null,
			"application": {
				"name": "Web",
				"website": null
			},
			"account": {
				"id": "109279987650139218",
				"username": "sepdroid",
				"acct": "sepdroid",
				"display_name": "Sepideh",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2022-11-03T00:00:00.000Z",
				"note": "<p>Android Dev with an interest in attending local meetings about K-12 education</p>",
				"url": "https://androiddev.social/@sepdroid",
				"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
				"header": "https://androiddev.social/headers/original/missing.png",
				"header_static": "https://androiddev.social/headers/original/missing.png",
				"followers_count": 144,
				"following_count": 305,
				"statuses_count": 355,
				"last_status_at": "2022-12-15",
				"noindex": false,
				"emojis": [],
				"fields": [
					{
						"name": "pronouns",
						"value": "she/her",
						"verified_at": null
					},
					{
						"name": "pronounced",
						"value": "Sa-pita",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [
				{
					"id": "109384856559579254",
					"username": "BenCisco",
					"url": "https://mastodon.online/@BenCisco",
					"acct": "<EMAIL>"
				}
			],
			"tags": [],
			"emojis": [],
			"card": {
				"url": "https://m.youtube.com/watch?v=WjuIcvQy8i4",
				"title": "The Neville Brothers - Everybody Plays The Fool - 10/31/1991 - Municipal Aud. N.O. (Official)",
				"description": "",
				"type": "video",
				"author_name": "Neville Brothers on MV",
				"author_url": "https://www.youtube.com/@NevilleBrothersOnMV",
				"provider_name": "YouTube",
				"provider_url": "https://www.youtube.com/",
				"html": "<iframe width=\"200\" height=\"150\" src=\"https://www.youtube.com/embed/WjuIcvQy8i4?feature=oembed\" frameborder=\"0\" allowfullscreen=\"\" title=\"The Neville Brothers - Everybody Plays The Fool - 10/31/1991 - Municipal Aud. N.O. (Official)\"></iframe>",
				"width": 200,
				"height": 150,
				"image": "https://cdn.masto.host/androiddevsocial/cache/preview_cards/images/000/211/271/original/b47ff33cac3bbe18.jpg",
				"embed_url": "",
				"blurhash": "U6DtVwReEkEe{m^85f]=9Xn,-A,]}bNZ-E9r"
			},
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109279987650139218",
			"username": "sepdroid",
			"acct": "sepdroid",
			"display_name": "Sepideh",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>Android Dev with an interest in attending local meetings about K-12 education</p>",
			"url": "https://androiddev.social/@sepdroid",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"header": "https://androiddev.social/headers/original/missing.png",
			"header_static": "https://androiddev.social/headers/original/missing.png",
			"followers_count": 144,
			"following_count": 305,
			"statuses_count": 355,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "pronouns",
					"value": "she/her",
					"verified_at": null
				},
				{
					"name": "pronounced",
					"value": "Sa-pita",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518252401798896",
		"created_at": "2022-12-15T14:51:51.892Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://androiddev.social/users/alanevans/statuses/109518252401798896",
		"url": "https://androiddev.social/@alanevans/109518252401798896",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 5,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>&quot;I&#39;m trying to sleep here! Kiki&quot; <a href=\"https://androiddev.social/tags/CatsOfMastodon\" class=\"mention hashtag\" rel=\"tag\">#<span>CatsOfMastodon</span></a></p>",
		"filtered": [],
		"reblog": null,
		"application": {
			"name": "Web",
			"website": null
		},
		"account": {
			"id": "109277745798323945",
			"username": "alanevans",
			"acct": "alanevans",
			"display_name": "Alan Evans",
			"locked": false,
			"bot": false,
			"discoverable": false,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>Android Dev. Formerly Twitter (1.0), Signal.</p>",
			"url": "https://androiddev.social/@alanevans",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/277/745/798/323/945/original/da167f8db31778e4.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/277/745/798/323/945/original/da167f8db31778e4.jpg",
			"header": "https://androiddev.social/headers/original/missing.png",
			"header_static": "https://androiddev.social/headers/original/missing.png",
			"followers_count": 61,
			"following_count": 93,
			"statuses_count": 96,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": []
		},
		"media_attachments": [
			{
				"id": "109518241013907255",
				"type": "image",
				"url": "https://cdn.masto.host/androiddevsocial/media_attachments/files/109/518/241/013/907/255/original/68334b6dd3934e49.jpeg",
				"preview_url": "https://cdn.masto.host/androiddevsocial/media_attachments/files/109/518/241/013/907/255/small/68334b6dd3934e49.jpeg",
				"remote_url": null,
				"preview_remote_url": null,
				"text_url": null,
				"meta": {
					"original": {
						"width": 1659,
						"height": 1249,
						"size": "1659x1249",
						"aspect": 1.****************
					},
					"small": {
						"width": 553,
						"height": 416,
						"size": "553x416",
						"aspect": 1.3293269230769231
					},
					"focus": {
						"x": -0.13,
						"y": 0.41
					}
				},
				"description": "Kiki standing on Mickey's bed watching birbs",
				"blurhash": "UYG+E#bI%3s:_NoKahkC%KR%NGoJaee:ofWB"
			},
			{
				"id": "109518241500933159",
				"type": "image",
				"url": "https://cdn.masto.host/androiddevsocial/media_attachments/files/109/518/241/500/933/159/original/413ca6949b75547c.jpeg",
				"preview_url": "https://cdn.masto.host/androiddevsocial/media_attachments/files/109/518/241/500/933/159/small/413ca6949b75547c.jpeg",
				"remote_url": null,
				"preview_remote_url": null,
				"text_url": null,
				"meta": {
					"original": {
						"width": 1659,
						"height": 1249,
						"size": "1659x1249",
						"aspect": 1.****************
					},
					"small": {
						"width": 553,
						"height": 416,
						"size": "553x416",
						"aspect": 1.3293269230769231
					},
					"focus": {
						"x": -0.18,
						"y": 0.14
					}
				},
				"description": "Close up of Mickey's face. He's curled up with one eye open.",
				"blurhash": "UQECta~q-p%MDhs;tSt7xvS3aeWA_2%KWnWo"
			}
		],
		"mentions": [],
		"tags": [
			{
				"name": "CatsOfMastodon",
				"url": "https://androiddev.social/tags/CatsOfMastodon"
			}
		],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518240079643348",
		"created_at": "2022-12-15T14:48:43.872Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/matt/statuses/109518240079643348/activity",
		"url": "https://androiddev.social/users/matt/statuses/109518240079643348/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109517964341845464",
			"created_at": "2022-12-15T13:38:36.453Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://androiddev.social/users/amanda/statuses/109517964341845464",
			"url": "https://androiddev.social/@amanda/109517964341845464",
			"replies_count": 0,
			"reblogs_count": 4,
			"favourites_count": 3,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>“A Not-so-scary Introduction to DP Mechanisms in Kotlin: Maximum Subarray” is out now🤠 <a href=\"https://androiddev.social/tags/Kotlin\" class=\"mention hashtag\" rel=\"tag\">#<span>Kotlin</span></a> </p><p><a href=\"https://medium.com/@hinchman-amanda/a-not-so-scary-introduction-to-dp-mechanisms-in-kotlin-maximum-subarray-767591f9ceeb\" target=\"_blank\" rel=\"nofollow noopener noreferrer\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">medium.com/@hinchman-amanda/a-</span><span class=\"invisible\">not-so-scary-introduction-to-dp-mechanisms-in-kotlin-maximum-subarray-767591f9ceeb</span></a></p>",
			"filtered": [],
			"reblog": null,
			"application": {
				"name": "Web",
				"website": null
			},
			"account": {
				"id": "109287686557350102",
				"username": "amanda",
				"acct": "amanda",
				"display_name": "Amanda Hinchman-Dominguez",
				"locked": false,
				"bot": false,
				"discoverable": false,
				"group": false,
				"created_at": "2022-11-04T00:00:00.000Z",
				"note": "<p>android engineer, Kotlin GDE, humble <br />@ChicagoKotlin<br /> organizer,  co-author of Programming Kotlin w/ Android: <a href=\"http://bit.ly/3G9qfN8\" target=\"_blank\" rel=\"nofollow noopener noreferrer\"><span class=\"invisible\">http://</span><span class=\"\">bit.ly/3G9qfN8</span><span class=\"invisible\"></span></a>, she/hers</p>",
				"url": "https://androiddev.social/@amanda",
				"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/287/686/557/350/102/original/609e7d27dad734e4.png",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/287/686/557/350/102/original/609e7d27dad734e4.png",
				"header": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/287/686/557/350/102/original/fe8e21b9b06eabab.png",
				"header_static": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/287/686/557/350/102/original/fe8e21b9b06eabab.png",
				"followers_count": 176,
				"following_count": 116,
				"statuses_count": 51,
				"last_status_at": "2022-12-15",
				"noindex": false,
				"emojis": [],
				"fields": [
					{
						"name": "#kotlin #android #tech",
						"value": "",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [
				{
					"name": "kotlin",
					"url": "https://androiddev.social/tags/kotlin"
				}
			],
			"emojis": [],
			"card": {
				"url": "https://hinchman-amanda.medium.com/a-not-so-scary-introduction-to-dp-mechanisms-in-kotlin-maximum-subarray-767591f9ceeb",
				"title": "A Not-so-scary Introduction to DP Mechanisms in Kotlin: Maximum Subarray",
				"description": "Dynamic programming (DP) can be an intimidating topic to learn. Give or take, I’ve arranged notes on the topic from previous job changes in the form of a digestible guide for quicker ramp-up. Less…",
				"type": "link",
				"author_name": "mvndy",
				"author_url": "https://hinchman-amanda.medium.com",
				"provider_name": "Medium",
				"provider_url": "",
				"html": "",
				"width": 400,
				"height": 267,
				"image": "https://cdn.masto.host/androiddevsocial/cache/preview_cards/images/000/210/801/original/de83a023c0cedd9c.jpg",
				"embed_url": "",
				"blurhash": "UCMP~#-@^w9GWNR8M_={-rSx%gJA%f-oD%NO"
			},
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109274112091517286",
			"username": "matt",
			"acct": "matt",
			"display_name": "Matt McKenna :androidEyes:",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-02T00:00:00.000Z",
			"note": "<p>he \\ him</p><p><a href=\"https://androiddev.social/tags/Android\" class=\"mention hashtag\" rel=\"tag\">#<span>Android</span></a> @ Square • Public Speaker • Community Enthusiast</p><p>I mostly post about <a href=\"https://androiddev.social/tags/AndroidDev\" class=\"mention hashtag\" rel=\"tag\">#<span>AndroidDev</span></a> and <a href=\"https://androiddev.social/tags/Kotlin\" class=\"mention hashtag\" rel=\"tag\">#<span>Kotlin</span></a>.</p><p>Let&#39;s talk about: <a href=\"https://androiddev.social/tags/Coffee\" class=\"mention hashtag\" rel=\"tag\">#<span>Coffee</span></a>, <a href=\"https://androiddev.social/tags/Esports\" class=\"mention hashtag\" rel=\"tag\">#<span>Esports</span></a>, <a href=\"https://androiddev.social/tags/Podcasts\" class=\"mention hashtag\" rel=\"tag\">#<span>Podcasts</span></a>, <a href=\"https://androiddev.social/tags/Reading\" class=\"mention hashtag\" rel=\"tag\">#<span>Reading</span></a>, <a href=\"https://androiddev.social/tags/Fitness\" class=\"mention hashtag\" rel=\"tag\">#<span>Fitness</span></a>.</p><p>androiddev.social admin</p><p><a href=\"https://androiddev.social/tags/BlackLivesMatter\" class=\"mention hashtag\" rel=\"tag\">#<span>BlackLivesMatter</span></a> <a href=\"https://androiddev.social/tags/StopAsianHate\" class=\"mention hashtag\" rel=\"tag\">#<span>StopAsianHate</span></a></p>",
			"url": "https://androiddev.social/@matt",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/274/112/091/517/286/original/ddfbe4fa528b3288.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/274/112/091/517/286/original/ddfbe4fa528b3288.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/274/112/091/517/286/original/2d5c8d906cfa288a.png",
			"header_static": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/274/112/091/517/286/original/2d5c8d906cfa288a.png",
			"followers_count": 1006,
			"following_count": 295,
			"statuses_count": 313,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [
				{
					"shortcode": "androidEyes",
					"url": "https://cdn.masto.host/androiddevsocial/custom_emojis/images/000/008/402/original/4d88e0b923ec4775.gif",
					"static_url": "https://cdn.masto.host/androiddevsocial/custom_emojis/images/000/008/402/static/4d88e0b923ec4775.png",
					"visible_in_picker": true
				}
			],
			"fields": [
				{
					"name": "✍️ blog",
					"value": "<a href=\"https://blog.mmckenna.me\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">blog.mmckenna.me</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-11-30T15:27:42.651+00:00"
				},
				{
					"name": "🎙️ live discussions",
					"value": "<a href=\"https://t.me/androiddevhang\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">t.me/androiddevhang</span><span class=\"invisible\"></span></a>",
					"verified_at": null
				},
				{
					"name": "🎧 podcast",
					"value": "<a href=\"https://androiddevdiscussions.substack.com\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">androiddevdiscussions.substack</span><span class=\"invisible\">.com</span></a>",
					"verified_at": null
				},
				{
					"name": "📸 photos",
					"value": "<a href=\"https://unsplash.com/mmckenna\" target=\"_blank\" rel=\"nofollow noopener noreferrer me\"><span class=\"invisible\">https://</span><span class=\"\">unsplash.com/mmckenna</span><span class=\"invisible\"></span></a>",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518224455955101",
		"created_at": "2022-12-15T14:44:44.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://mastodon.xyz/users/anthonycr/statuses/109518224409509568",
		"url": "https://mastodon.xyz/@anthonycr/109518224409509568",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>thankfully there's a way to customize what syncs</p>",
		"filtered": [],
		"reblog": null,
		"account": {
			"id": "109274342909461515",
			"username": "anthonycr",
			"acct": "<EMAIL>",
			"display_name": "anthony restaino",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2017-04-04T00:00:00.000Z",
			"note": "<p>android dev @ zillow | ex vimeo | pro socialism | 🥕 food security | 🍳 cooking | 🦆 birding | 📍 brooklyn | he/they</p>",
			"url": "https://mastodon.xyz/@anthonycr",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/342/909/461/515/original/9cb3472081718e2f.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/342/909/461/515/original/9cb3472081718e2f.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/342/909/461/515/original/8b3baed8a39be136.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/342/909/461/515/original/8b3baed8a39be136.jpeg",
			"followers_count": 49,
			"following_count": 102,
			"statuses_count": 156,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": []
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518218443663652",
		"created_at": "2022-12-15T14:43:12.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://mastodon.xyz/users/anthonycr/statuses/109518218332858107",
		"url": "https://mastodon.xyz/@anthonycr/109518218332858107",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 1,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>also have found the cross device session syncing pretty horrible for use at the food pantry, which is signed into multiple devices with the same account. filling out the same form simultaneously on separate devices results in your input being overwritten while you input it.</p>",
		"filtered": [],
		"reblog": null,
		"account": {
			"id": "109274342909461515",
			"username": "anthonycr",
			"acct": "<EMAIL>",
			"display_name": "anthony restaino",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2017-04-04T00:00:00.000Z",
			"note": "<p>android dev @ zillow | ex vimeo | pro socialism | 🥕 food security | 🍳 cooking | 🦆 birding | 📍 brooklyn | he/they</p>",
			"url": "https://mastodon.xyz/@anthonycr",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/342/909/461/515/original/9cb3472081718e2f.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/342/909/461/515/original/9cb3472081718e2f.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/342/909/461/515/original/8b3baed8a39be136.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/342/909/461/515/original/8b3baed8a39be136.jpeg",
			"followers_count": 49,
			"following_count": 102,
			"statuses_count": 156,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": []
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518208846173703",
		"created_at": "2022-12-15T14:40:46.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": "en",
		"uri": "https://mastodon.xyz/users/anthonycr/statuses/109518208796570539",
		"url": "https://mastodon.xyz/@anthonycr/109518208796570539",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 1,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "<p>chrome state syncing across devices is a great feature, but lately it's been pretty inconvenient. It's now syncing full screen state across devices, which has confused me multiple times when watching something full screen on one device while working on another.</p>",
		"filtered": [],
		"reblog": null,
		"account": {
			"id": "109274342909461515",
			"username": "anthonycr",
			"acct": "<EMAIL>",
			"display_name": "anthony restaino",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2017-04-04T00:00:00.000Z",
			"note": "<p>android dev @ zillow | ex vimeo | pro socialism | 🥕 food security | 🍳 cooking | 🦆 birding | 📍 brooklyn | he/they</p>",
			"url": "https://mastodon.xyz/@anthonycr",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/342/909/461/515/original/9cb3472081718e2f.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/342/909/461/515/original/9cb3472081718e2f.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/342/909/461/515/original/8b3baed8a39be136.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/342/909/461/515/original/8b3baed8a39be136.jpeg",
			"followers_count": 49,
			"following_count": 102,
			"statuses_count": 156,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": []
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518108854648598",
		"created_at": "2022-12-15T14:15:21.538Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/sepdroid/statuses/109518108854648598/activity",
		"url": "https://androiddev.social/users/sepdroid/statuses/109518108854648598/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109490318507587844",
			"created_at": "2022-12-10T16:27:54.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://sauropods.win/users/futurebird/statuses/109490318505938606",
			"url": "https://sauropods.win/@futurebird/109490318505938606",
			"replies_count": 12,
			"reblogs_count": 43,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>When I was active at the wikipedia there were a group of other writers who would follow my around and mark my articles on women and black people for deletion. They marked my edits CN even when there was a citation. They reworded everything I wrote to minimize the contributions of minorities to history, and hint at theories of racial inferiority.  I'd just go to the library and bury them in more citations. It was kind of violent TBH.</p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109301253200663189",
				"username": "futurebird",
				"acct": "<EMAIL>",
				"display_name": "myrmepropagandist",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2022-11-07T00:00:00.000Z",
				"note": "<p>pro-ant propaganda, building electronics, writing sci-fi teaching mathematics &amp; CS.  I live in NYC.</p>",
				"url": "https://sauropods.win/@futurebird",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/301/253/200/663/189/original/6e04b975b62c53cc.png",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/301/253/200/663/189/original/6e04b975b62c53cc.png",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/301/253/200/663/189/original/20174b79c2849673.png",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/301/253/200/663/189/original/20174b79c2849673.png",
				"followers_count": 2478,
				"following_count": 1171,
				"statuses_count": 2529,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "tumblr",
						"value": "<a href=\"https://futurebird.tumblr.com/\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">futurebird.tumblr.com/</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-12-15T12:59:28.451+00:00"
					},
					{
						"name": "twitter",
						"value": "<a href=\"https://twitter.com/futurebird\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">twitter.com/futurebird</span><span class=\"invisible\"></span></a>",
						"verified_at": null
					},
					{
						"name": "pronouns",
						"value": "she/her/lady/ma'am",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": null,
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109279987650139218",
			"username": "sepdroid",
			"acct": "sepdroid",
			"display_name": "Sepideh",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>Android Dev with an interest in attending local meetings about K-12 education</p>",
			"url": "https://androiddev.social/@sepdroid",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"header": "https://androiddev.social/headers/original/missing.png",
			"header_static": "https://androiddev.social/headers/original/missing.png",
			"followers_count": 144,
			"following_count": 305,
			"statuses_count": 355,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "pronouns",
					"value": "she/her",
					"verified_at": null
				},
				{
					"name": "pronounced",
					"value": "Sa-pita",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518085821937371",
		"created_at": "2022-12-15T14:09:30.087Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/sepdroid/statuses/109518085821937371/activity",
		"url": "https://androiddev.social/users/sepdroid/statuses/109518085821937371/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109514962581691447",
			"created_at": "2022-12-15T00:55:13.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://econtwitter.net/users/m_clem/statuses/109514962575202965",
			"url": "https://econtwitter.net/@m_clem/109514962575202965",
			"replies_count": 0,
			"reblogs_count": 38,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>The Boston University School of Public Health is withdrawing from Twitter. </p><p>The dean’s thoughtful, nuanced explanation raises the question: Why are other institutions still there?</p><p><a href=\"https://www.bu.edu/sph/news/articles/2022/reconsidering-our-engagement-with-twitter/\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://www.</span><span class=\"ellipsis\">bu.edu/sph/news/articles/2022/</span><span class=\"invisible\">reconsidering-our-engagement-with-twitter/</span></a></p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109320824540850157",
				"username": "m_clem",
				"acct": "<EMAIL>",
				"display_name": "Michael Clemens",
				"locked": false,
				"bot": false,
				"discoverable": false,
				"group": false,
				"created_at": "2022-11-05T00:00:00.000Z",
				"note": "<p>I'm an economist who studies the causes and effects of international migration. Fellow at Center for Global Development, IZA Bonn, and CReAM~UCL. Personal views only.</p>",
				"url": "https://econtwitter.net/@m_clem",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/320/824/540/850/157/original/bbc98d52d7a4951b.png",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/320/824/540/850/157/original/bbc98d52d7a4951b.png",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/320/824/540/850/157/original/e5bccf4a36d097b8.jpeg",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/320/824/540/850/157/original/e5bccf4a36d097b8.jpeg",
				"followers_count": 1665,
				"following_count": 737,
				"statuses_count": 548,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "Web",
						"value": "<a href=\"http://mclem.org\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">http://</span><span class=\"\">mclem.org</span><span class=\"invisible\"></span></a>",
						"verified_at": null
					},
					{
						"name": "Verification",
						"value": "<a href=\"https://ideas.repec.org/e/pcl20.html\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">ideas.repec.org/e/pcl20.html</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-12-15T04:02:04.295+00:00"
					}
				]
			},
			"media_attachments": [
				{
					"id": "109515697286984687",
					"type": "image",
					"url": "https://cdn.masto.host/androiddevsocial/cache/media_attachments/files/109/515/697/286/984/687/original/ff690f7bff2fede6.jpeg",
					"preview_url": "https://cdn.masto.host/androiddevsocial/cache/media_attachments/files/109/515/697/286/984/687/small/ff690f7bff2fede6.jpeg",
					"remote_url": "https://cdn.masto.host/econtwitternet/media_attachments/files/109/514/961/521/135/485/original/816a885271e3335a.jpeg",
					"preview_remote_url": null,
					"text_url": null,
					"meta": {
						"original": {
							"width": 1280,
							"height": 570,
							"size": "1280x570",
							"aspect": 2.245614035087719
						},
						"small": {
							"width": 719,
							"height": 320,
							"size": "719x320",
							"aspect": 2.246875
						}
					},
					"description": null,
					"blurhash": "UKRfkBj[_3WB-;ayM{kB~qj[NFofRjofayWB"
				}
			],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": {
				"url": "https://www.bu.edu/sph/news/articles/2022/reconsidering-our-engagement-with-twitter/",
				"title": "Reconsidering our School's Engagement with Twitter",
				"description": "",
				"type": "link",
				"author_name": "",
				"author_url": "",
				"provider_name": "",
				"provider_url": "",
				"html": "",
				"width": 400,
				"height": 241,
				"image": "https://cdn.masto.host/androiddevsocial/cache/preview_cards/images/000/206/663/original/c221c2ef4fbafb11.jpg",
				"embed_url": "",
				"blurhash": "UwI}V9Rjx^R+~qRkkXWC%NWXf+ayo}WCjaay"
			},
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109279987650139218",
			"username": "sepdroid",
			"acct": "sepdroid",
			"display_name": "Sepideh",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>Android Dev with an interest in attending local meetings about K-12 education</p>",
			"url": "https://androiddev.social/@sepdroid",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"header": "https://androiddev.social/headers/original/missing.png",
			"header_static": "https://androiddev.social/headers/original/missing.png",
			"followers_count": 144,
			"following_count": 305,
			"statuses_count": 355,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "pronouns",
					"value": "she/her",
					"verified_at": null
				},
				{
					"name": "pronounced",
					"value": "Sa-pita",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518029301323901",
		"created_at": "2022-12-15T13:54:40.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://toot.thoughtworks.com/users/mfowler/statuses/109518027501299025/activity",
		"url": null,
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109517740964906428",
			"created_at": "2022-12-15T12:41:47.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://mastodon.social/users/kevlin/statuses/109517740925744193",
			"url": "https://mastodon.social/@kevlin/109517740925744193",
			"replies_count": 6,
			"reblogs_count": 5,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>I have found myself teaching and encouraging teams to use ADRs more and more in recent years.</p><p>I am, however, constantly surprised by the failure modes that people find themselves in, typically through no fault of their own.</p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109293525135786218",
				"username": "kevlin",
				"acct": "<EMAIL>",
				"display_name": "Kevlin Henney",
				"locked": false,
				"bot": false,
				"discoverable": false,
				"group": false,
				"created_at": "2016-11-01T00:00:00.000Z",
				"note": "<p>consultant · father · he/him · human (very) · husband · itinerant · programmer · keynote speaker · technologist · trainer · writer</p>",
				"url": "https://mastodon.social/@kevlin",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/293/525/135/786/218/original/08666369be735602.jpg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/293/525/135/786/218/original/08666369be735602.jpg",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/293/525/135/786/218/original/3ca14b8be7718a8b.jpg",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/293/525/135/786/218/original/3ca14b8be7718a8b.jpg",
				"followers_count": 489,
				"following_count": 51,
				"statuses_count": 195,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "Location",
						"value": "☉+~1au",
						"verified_at": null
					},
					{
						"name": "Blog",
						"value": "<a href=\"https://kevlinhenney.medium.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">kevlinhenney.medium.com</span><span class=\"invisible\"></span></a>",
						"verified_at": null
					},
					{
						"name": "About",
						"value": "<a href=\"https://about.me/kevlin\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">about.me/kevlin</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-11-20T11:56:07.993+00:00"
					},
					{
						"name": "Contact",
						"value": "<a href=\"http://kevlin.tel\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">http://</span><span class=\"\">kevlin.tel</span><span class=\"invisible\"></span></a>",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": null,
			"poll": null
		},
		"account": {
			"id": "109274267286908118",
			"username": "mfowler",
			"acct": "<EMAIL>",
			"display_name": "Martin Fowler",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-04-25T00:00:00.000Z",
			"note": "<p>Author and loudmouth on software development. Works at Thoughtworks.  Also hikes, watches theater, and plays modern board games.</p>",
			"url": "https://toot.thoughtworks.com/@mfowler",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/267/286/908/118/original/da3fd245793af66e.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/267/286/908/118/original/da3fd245793af66e.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/267/286/908/118/original/40758799f029b7eb.jpeg",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/267/286/908/118/original/40758799f029b7eb.jpeg",
			"followers_count": 10106,
			"following_count": 79,
			"statuses_count": 151,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": [
				{
					"name": "Website",
					"value": "<a href=\"https://martinfowler.com\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">martinfowler.com</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-06T20:47:24.044+00:00"
				},
				{
					"name": "Pronouns",
					"value": "he/him",
					"verified_at": null
				},
				{
					"name": "email",
					"value": "<EMAIL>",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518014665598096",
		"created_at": "2022-12-15T13:51:24.329Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "unlisted",
		"language": null,
		"uri": "https://androiddev.social/users/marcin/statuses/109518014665598096/activity",
		"url": "https://androiddev.social/users/marcin/statuses/109518014665598096/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109516507746082589",
			"created_at": "2022-12-15T07:28:03.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://botsin.space/users/APoD/statuses/109516507295806068",
			"url": "https://botsin.space/@APoD/109516507295806068",
			"replies_count": 4,
			"reblogs_count": 2,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>Full Moon, Full Mars</p><p>Image Credit &amp; Copyright: Tomas Slovinsky</p><p><a href=\"https://apod.nasa.gov/apod/ap221215.html\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">apod.nasa.gov/apod/ap221215.ht</span><span class=\"invisible\">ml</span></a> <a href=\"https://botsin.space/tags/APOD\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>APOD</span></a></p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109274516021668243",
				"username": "APoD",
				"acct": "<EMAIL>",
				"display_name": "Astronomy Picture of the Day",
				"locked": false,
				"bot": true,
				"discoverable": true,
				"group": false,
				"created_at": "2018-01-12T00:00:00.000Z",
				"note": "<p>Discover the cosmos! A different image of our fascinating universe every day. </p><p>🌌 <a href=\"https://apod.nasa.gov/\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">apod.nasa.gov/</span><span class=\"invisible\"></span></a></p><p>(Automated mirror, supervised by <span class=\"h-card\"><a href=\"https://chitter.xyz/@codl\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>codl</span></a></span>, who is not an astronomer)</p>",
				"url": "https://botsin.space/@APoD",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/516/021/668/243/original/971cc825721fd263.jpg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/274/516/021/668/243/original/971cc825721fd263.jpg",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/516/021/668/243/original/545129759d5dbf16.jpg",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/274/516/021/668/243/original/545129759d5dbf16.jpg",
				"followers_count": 53512,
				"following_count": 1,
				"statuses_count": 1809,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "Banner",
						"value": "<a href=\"https://apod.nasa.gov/apod/ap131231.html\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">apod.nasa.gov/apod/ap131231.ht</span><span class=\"invisible\">ml</span></a>",
						"verified_at": null
					},
					{
						"name": "Avatar",
						"value": "<a href=\"https://apod.nasa.gov/apod/ap220206.html\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">apod.nasa.gov/apod/ap220206.ht</span><span class=\"invisible\">ml</span></a>",
						"verified_at": null
					}
				]
			},
			"media_attachments": [
				{
					"id": "109516507668319090",
					"type": "image",
					"url": "https://cdn.masto.host/androiddevsocial/cache/media_attachments/files/109/516/507/668/319/090/original/b4a088d70fddd91c.jpg",
					"preview_url": "https://cdn.masto.host/androiddevsocial/cache/media_attachments/files/109/516/507/668/319/090/small/b4a088d70fddd91c.jpg",
					"remote_url": "https://files.botsin.space/media_attachments/files/109/516/507/282/596/167/original/bfa8cfcd97bb8116.jpg",
					"preview_remote_url": null,
					"text_url": null,
					"meta": {
						"original": {
							"width": 1080,
							"height": 873,
							"size": "1080x873",
							"aspect": 1.2371134020618557
						},
						"small": {
							"width": 534,
							"height": 432,
							"size": "534x432",
							"aspect": 1.2361111111111112
						}
					},
					"description": null,
					"blurhash": "UTBo:PWU0gaJSNazn[0gjF?Fg3r=j@S"
				}
			],
			"mentions": [],
			"tags": [
				{
					"name": "apod",
					"url": "https://androiddev.social/tags/apod"
				}
			],
			"emojis": [],
			"card": {
				"url": "https://apod.nasa.gov/apod/ap221215.html",
				"title": " APOD: 2022 December 15 - Full Moon, Full Mars\n",
				"description": "A different astronomy and space science\nrelated image is featured each day, along with a brief explanation.",
				"type": "link",
				"author_name": "",
				"author_url": "",
				"provider_name": "",
				"provider_url": "",
				"html": "",
				"width": 0,
				"height": 0,
				"image": null,
				"embed_url": "",
				"blurhash": null
			},
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109279028731807625",
			"username": "marcin",
			"acct": "marcin",
			"display_name": "Marcin ✌️",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>I like to create things. Often the things are Android apps. Currently working at Pocket/Mozilla. Białystok, Poland 🇪🇺</p><p>One of my pronouns is half a giggle. (h/t <span class=\"h-card\"><a href=\"https://lesbian.solutions/@stella\" class=\"u-url mention\">@<span>stella</span></a></span>)</p>",
			"url": "https://androiddev.social/@marcin",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/028/731/807/625/original/c91f0af67ed61e9e.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/028/731/807/625/original/c91f0af67ed61e9e.jpg",
			"header": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/279/028/731/807/625/original/58deb9050048d26e.jpg",
			"header_static": "https://cdn.masto.host/androiddevsocial/accounts/headers/109/279/028/731/807/625/original/58deb9050048d26e.jpg",
			"followers_count": 116,
			"following_count": 124,
			"statuses_count": 158,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "pronouns",
					"value": "he/him",
					"verified_at": null
				},
				{
					"name": "pronounced",
					"value": "marchin&#39;",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109518003607484488",
		"created_at": "2022-12-15T13:48:27.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://macaw.social/users/andypiper/statuses/109518003092739386/activity",
		"url": null,
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109517747119542890",
			"created_at": "2022-12-15T12:43:21.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://chaos.social/users/attie/statuses/109517747098278057",
			"url": "https://chaos.social/@attie/109517747098278057",
			"replies_count": 0,
			"reblogs_count": 1,
			"favourites_count": 0,
			"edited_at": "2022-12-15T12:43:58.000Z",
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>My session with <span class=\"h-card\"><a href=\"https://macaw.social/@andypiper\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>andypiper</span></a></span> got a mention in <span class=\"h-card\"><a href=\"https://octodon.social/@Anneb\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>Anneb</span></a></span>'s post on the Adafruit Blog (yay, thank you! 🥳)</p><p><a href=\"https://blog.adafruit.com/2022/12/14/icymi-python-on-microcontrollers-newsletter-circuitpython-8-beta-5-released-more-raspberry-pis-are-coming-more-circuitpython-icymi-micropython-raspberry_pi/\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"ellipsis\">blog.adafruit.com/2022/12/14/i</span><span class=\"invisible\">cymi-python-on-microcontrollers-newsletter-circuitpython-8-beta-5-released-more-raspberry-pis-are-coming-more-circuitpython-icymi-micropython-raspberry_pi/</span></a></p><p><a href=\"https://www.youtube.com/watch?v=jktFR0vpHgU\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://www.</span><span class=\"ellipsis\">youtube.com/watch?v=jktFR0vpHg</span><span class=\"invisible\">U</span></a></p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109282499086047503",
				"username": "attie",
				"acct": "<EMAIL>",
				"display_name": "Attie Grande",
				"locked": false,
				"bot": false,
				"discoverable": true,
				"group": false,
				"created_at": "2022-10-29T00:00:00.000Z",
				"note": "<p>computer booper / electronics wizard.<br>things are never as simple as they first appear.<br>ally 🌈🦕🦄</p>",
				"url": "https://chaos.social/@attie",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/282/499/086/047/503/original/786719017480ebc0.png",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/282/499/086/047/503/original/786719017480ebc0.png",
				"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/282/499/086/047/503/original/d01833cc31e1ea03.jpeg",
				"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/282/499/086/047/503/original/d01833cc31e1ea03.jpeg",
				"followers_count": 406,
				"following_count": 544,
				"statuses_count": 229,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "Pronouns",
						"value": "he/him/they",
						"verified_at": null
					},
					{
						"name": "Website",
						"value": "<a href=\"https://attie.co.uk\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">attie.co.uk</span><span class=\"invisible\"></span></a>",
						"verified_at": "2022-12-15T13:48:26.666+00:00"
					},
					{
						"name": "Email",
						"value": "<EMAIL>",
						"verified_at": null
					},
					{
						"name": "Twitter",
						"value": "<a href=\"https://twitter.com/AttieGrande\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">twitter.com/AttieGrande</span><span class=\"invisible\"></span></a>",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [
				{
					"id": "109349440230215508",
					"username": "andypiper",
					"url": "https://macaw.social/@andypiper",
					"acct": "<EMAIL>"
				},
				{
					"id": "109367220441922940",
					"username": "Anneb",
					"url": "https://octodon.social/@Anneb",
					"acct": "<EMAIL>"
				}
			],
			"tags": [],
			"emojis": [],
			"card": {
				"url": "https://blog.adafruit.com/2022/12/14/icymi-python-on-microcontrollers-newsletter-circuitpython-8-beta-5-released-more-raspberry-pis-are-coming-more-circuitpython-icymi-micropython-raspberry_pi/",
				"title": "ICYMI Python on Microcontrollers Newsletter: CircuitPython 8 beta 5 Released, More Raspberry Pis are Coming & More! #CircuitPython #ICYMI @micropython @Raspberry_Pi",
				"description": "If you missed Tuesday’s Python on Microcontrollers Newsletter, here is the ICYMI (in case you missed it) version. To never miss another issue, subscribe now! – You’ll get one terrific newsletter ea…",
				"type": "link",
				"author_name": "",
				"author_url": "",
				"provider_name": "Adafruit Industries - Makers, hackers, artists, designers and engineers!",
				"provider_url": "",
				"html": "",
				"width": 400,
				"height": 129,
				"image": "https://cdn.masto.host/androiddevsocial/cache/preview_cards/images/000/210/856/original/7111fe2f13388f70.jpg",
				"embed_url": "",
				"blurhash": "UXR3NFV[~qtQ-=t6MxM}kDNGae%2InoMxtR%"
			},
			"poll": null
		},
		"account": {
			"id": "109349440230215508",
			"username": "andypiper",
			"acct": "<EMAIL>",
			"display_name": "Andy Piper",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-12T00:00:00.000Z",
			"note": "<p>Tech speaker, developer advocate <a href=\"https://macaw.social/tags/DevRel\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>DevRel</span></a>, supporter, communities person 💗💜💙 I do things with code, and tinker with gadgets <a href=\"https://macaw.social/tags/MicroPython\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>MicroPython</span></a> <a href=\"https://macaw.social/tags/MQTT\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>MQTT</span></a> <a href=\"https://macaw.social/tags/LEGO\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>LEGO</span></a> <a href=\"https://macaw.social/tags/BoardGames\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>BoardGames</span></a> <a href=\"https://macaw.social/tags/DoctorWho\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>DoctorWho</span></a> … I am one-third of a weekly <a href=\"https://macaw.social/tags/podcast\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>podcast</span></a> 🎧<span class=\"h-card\"><a href=\"https://botsin.space/@gamesatwork_biz\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>gamesatwork_biz</span></a></span> and help with a <a href=\"https://macaw.social/tags/hackspace\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>hackspace</span></a> meetup <span class=\"h-card\"><a href=\"https://fosstodon.org/@makeroni\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>makeroni</span></a></span></p>",
			"url": "https://macaw.social/@andypiper",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/349/440/230/215/508/original/9a56ec2d87360520.jpeg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/349/440/230/215/508/original/9a56ec2d87360520.jpeg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/349/440/230/215/508/original/3cb6c7c7b9655877.png",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/349/440/230/215/508/original/3cb6c7c7b9655877.png",
			"followers_count": 2243,
			"following_count": 1609,
			"statuses_count": 741,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": [
				{
					"name": "🗺️ links",
					"value": "<a href=\"https://andypiper.me\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">andypiper.me</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-14T08:17:09.034+00:00"
				},
				{
					"name": "🎧 podcast",
					"value": "<a href=\"https://gamesatwork.biz\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">gamesatwork.biz</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-14T08:17:10.859+00:00"
				},
				{
					"name": "🧑🏼‍💻 code",
					"value": "<a href=\"https://github.com/andypiper\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">github.com/andypiper</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-14T08:17:11.853+00:00"
				},
				{
					"name": "👤 pronouns",
					"value": "he/they/them",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109517998820557382",
		"created_at": "2022-12-15T13:47:14.000Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://macaw.social/users/andypiper/statuses/109517998271758059/activity",
		"url": null,
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109513081884274482",
			"created_at": "2022-12-14T16:56:56.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://wandering.shop/users/indeed_distract/statuses/109513081910999351",
			"url": "https://wandering.shop/@indeed_distract/109513081910999351",
			"replies_count": 1,
			"reblogs_count": 6,
			"favourites_count": 0,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>Gyre *and* gimble? In this wabe?</p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109304702260258553",
				"username": "indeed_distract",
				"acct": "<EMAIL>",
				"display_name": "Andy H.",
				"locked": false,
				"bot": false,
				"discoverable": false,
				"group": false,
				"created_at": "2017-10-13T00:00:00.000Z",
				"note": "<p>Precision-seeking, but often ridiculous.</p>",
				"url": "https://wandering.shop/@indeed_distract",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/304/702/260/258/553/original/a45c891c4fa356cf.jpg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/304/702/260/258/553/original/a45c891c4fa356cf.jpg",
				"header": "https://androiddev.social/headers/original/missing.png",
				"header_static": "https://androiddev.social/headers/original/missing.png",
				"followers_count": 32,
				"following_count": 96,
				"statuses_count": 41,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": [
					{
						"name": "pronouns",
						"value": "he/him",
						"verified_at": null
					}
				]
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [],
			"emojis": [],
			"card": null,
			"poll": null
		},
		"account": {
			"id": "109349440230215508",
			"username": "andypiper",
			"acct": "<EMAIL>",
			"display_name": "Andy Piper",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-12T00:00:00.000Z",
			"note": "<p>Tech speaker, developer advocate <a href=\"https://macaw.social/tags/DevRel\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>DevRel</span></a>, supporter, communities person 💗💜💙 I do things with code, and tinker with gadgets <a href=\"https://macaw.social/tags/MicroPython\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>MicroPython</span></a> <a href=\"https://macaw.social/tags/MQTT\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>MQTT</span></a> <a href=\"https://macaw.social/tags/LEGO\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>LEGO</span></a> <a href=\"https://macaw.social/tags/BoardGames\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>BoardGames</span></a> <a href=\"https://macaw.social/tags/DoctorWho\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>DoctorWho</span></a> … I am one-third of a weekly <a href=\"https://macaw.social/tags/podcast\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>podcast</span></a> 🎧<span class=\"h-card\"><a href=\"https://botsin.space/@gamesatwork_biz\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>gamesatwork_biz</span></a></span> and help with a <a href=\"https://macaw.social/tags/hackspace\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>hackspace</span></a> meetup <span class=\"h-card\"><a href=\"https://fosstodon.org/@makeroni\" class=\"u-url mention\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">@<span>makeroni</span></a></span></p>",
			"url": "https://macaw.social/@andypiper",
			"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/349/440/230/215/508/original/9a56ec2d87360520.jpeg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/349/440/230/215/508/original/9a56ec2d87360520.jpeg",
			"header": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/349/440/230/215/508/original/3cb6c7c7b9655877.png",
			"header_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/headers/109/349/440/230/215/508/original/3cb6c7c7b9655877.png",
			"followers_count": 2243,
			"following_count": 1609,
			"statuses_count": 741,
			"last_status_at": "2022-12-15",
			"emojis": [],
			"fields": [
				{
					"name": "🗺️ links",
					"value": "<a href=\"https://andypiper.me\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">andypiper.me</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-14T08:17:09.034+00:00"
				},
				{
					"name": "🎧 podcast",
					"value": "<a href=\"https://gamesatwork.biz\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">gamesatwork.biz</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-14T08:17:10.859+00:00"
				},
				{
					"name": "🧑🏼‍💻 code",
					"value": "<a href=\"https://github.com/andypiper\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">github.com/andypiper</span><span class=\"invisible\"></span></a>",
					"verified_at": "2022-12-14T08:17:11.853+00:00"
				},
				{
					"name": "👤 pronouns",
					"value": "he/they/them",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	},
	{
		"id": "109517947023389164",
		"created_at": "2022-12-15T13:34:12.190Z",
		"in_reply_to_id": null,
		"in_reply_to_account_id": null,
		"sensitive": false,
		"spoiler_text": "",
		"visibility": "public",
		"language": null,
		"uri": "https://androiddev.social/users/sepdroid/statuses/109517947023389164/activity",
		"url": "https://androiddev.social/users/sepdroid/statuses/109517947023389164/activity",
		"replies_count": 0,
		"reblogs_count": 0,
		"favourites_count": 0,
		"edited_at": null,
		"favourited": false,
		"reblogged": false,
		"muted": false,
		"bookmarked": false,
		"content": "",
		"filtered": [],
		"reblog": {
			"id": "109515422931555103",
			"created_at": "2022-12-15T02:52:16.000Z",
			"in_reply_to_id": null,
			"in_reply_to_account_id": null,
			"sensitive": false,
			"spoiler_text": "",
			"visibility": "public",
			"language": "en",
			"uri": "https://hachyderm.io/users/juliet/statuses/109515422878602532",
			"url": "https://hachyderm.io/@juliet/109515422878602532",
			"replies_count": 1,
			"reblogs_count": 1,
			"favourites_count": 1,
			"edited_at": null,
			"favourited": false,
			"reblogged": false,
			"muted": false,
			"bookmarked": false,
			"content": "<p>I'm polishing and practicing my talk for <a href=\"https://hachyderm.io/tags/normconf\" class=\"mention hashtag\" rel=\"nofollow noopener noreferrer\" target=\"_blank\">#<span>normconf</span></a> tomorrow! I'm talking about The Physics of Data aka \"omg you mean I need to know how computers work?\"</p><p>Catch me live at 6 PM Pacific time!<br><a href=\"https://normconf.com/\" rel=\"nofollow noopener noreferrer\" target=\"_blank\"><span class=\"invisible\">https://</span><span class=\"\">normconf.com/</span><span class=\"invisible\"></span></a></p>",
			"filtered": [],
			"reblog": null,
			"account": {
				"id": "109388149421346346",
				"username": "juliet",
				"acct": "<EMAIL>",
				"display_name": "Juliet Hougland",
				"locked": false,
				"bot": false,
				"discoverable": false,
				"group": false,
				"created_at": "2022-11-22T00:00:00.000Z",
				"note": "<p>Sicker than your average. I like plants, weightlifting, and making computers do math for me.</p>",
				"url": "https://hachyderm.io/@juliet",
				"avatar": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/388/149/421/346/346/original/09a8f53c72a8bad8.jpeg",
				"avatar_static": "https://cdn.masto.host/androiddevsocial/cache/accounts/avatars/109/388/149/421/346/346/original/09a8f53c72a8bad8.jpeg",
				"header": "https://androiddev.social/headers/original/missing.png",
				"header_static": "https://androiddev.social/headers/original/missing.png",
				"followers_count": 0,
				"following_count": 10,
				"statuses_count": 20,
				"last_status_at": "2022-12-15",
				"emojis": [],
				"fields": []
			},
			"media_attachments": [],
			"mentions": [],
			"tags": [
				{
					"name": "NormConf",
					"url": "https://androiddev.social/tags/NormConf"
				}
			],
			"emojis": [],
			"card": {
				"url": "https://normconf.com",
				"title": "Normconf",
				"description": "Normconf is the tech conference about all the stuff that matters in data and machine learning, but doesn't get the spotlight",
				"type": "link",
				"author_name": "",
				"author_url": "",
				"provider_name": "",
				"provider_url": "",
				"html": "",
				"width": 400,
				"height": 200,
				"image": "https://cdn.masto.host/androiddevsocial/cache/preview_cards/images/000/031/200/original/e553e28424ebf3f0.png",
				"embed_url": "",
				"blurhash": "UMRW9%7X?H[wJ#Ns~V;9M{Fs~C-CELEL"
			},
			"poll": null
		},
		"application": null,
		"account": {
			"id": "109279987650139218",
			"username": "sepdroid",
			"acct": "sepdroid",
			"display_name": "Sepideh",
			"locked": false,
			"bot": false,
			"discoverable": true,
			"group": false,
			"created_at": "2022-11-03T00:00:00.000Z",
			"note": "<p>Android Dev with an interest in attending local meetings about K-12 education</p>",
			"url": "https://androiddev.social/@sepdroid",
			"avatar": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"avatar_static": "https://cdn.masto.host/androiddevsocial/accounts/avatars/109/279/987/650/139/218/original/47bf288270807ee4.jpg",
			"header": "https://androiddev.social/headers/original/missing.png",
			"header_static": "https://androiddev.social/headers/original/missing.png",
			"followers_count": 144,
			"following_count": 305,
			"statuses_count": 355,
			"last_status_at": "2022-12-15",
			"noindex": false,
			"emojis": [],
			"fields": [
				{
					"name": "pronouns",
					"value": "she/her",
					"verified_at": null
				},
				{
					"name": "pronounced",
					"value": "Sa-pita",
					"verified_at": null
				}
			]
		},
		"media_attachments": [],
		"mentions": [],
		"tags": [],
		"emojis": [],
		"card": null,
		"poll": null
	}
]
    """.trimIndent()
