package com.amoretech.memory.data.network.model

import kotlinx.serialization.json.Json
import kotlin.test.Test
import kotlin.test.assertEquals

class HistoryTests {
    @Test
    fun `deserialize required fields should succeed`() {
        // given
        val json =
            """
            {
                "day": "**********",
                "uses": "200",
                "accounts": "31"
            }
            """.trimIndent()

        // when
        val history = Json.decodeFromString<History>(json)

        // then
        assertEquals(expected = "**********", actual = history.day)
        assertEquals(expected = "200", actual = history.uses)
        assertEquals(expected = "31", actual = history.accounts)
    }
}
