package com.amoretech.memory.data.network.model

import kotlinx.serialization.json.Json
import kotlin.test.Test
import kotlin.test.assertEquals

class TagTests {
    @Test
    fun `deserialize required fields should succeed`() {
        // given
        val json =
            """
            {
                "name": "nowplaying",
                "url": "https://mastodon.social/tags/nowplaying",
                "history":
                [
                    {
                        "day": "**********",
                        "uses": "200",
                        "accounts": "31"
                    },
                    {
                        "day": "**********",
                        "uses": "272",
                        "accounts": "39"
                    },
                    {
                        "day": "**********",
                        "uses": "345",
                        "accounts": "40"
                    },
                    {
                        "day": "**********",
                        "uses": "366",
                        "accounts": "46"
                    },
                    {
                        "day": "**********",
                        "uses": "226",
                        "accounts": "32"
                    },
                    {
                        "day": "**********",
                        "uses": "217",
                        "accounts": "42"
                    },
                    {
                        "day": "**********",
                        "uses": "214",
                        "accounts": "34"
                    }
                ]
            }
            """.trimIndent()

        // when
        val tag = Json.decodeFromString<Tag>(json)

        // then
        val day1 = History("**********", "200", "31")
        val day2 = History("**********", "272", "39")
        val day3 = History("**********", "345", "40")
        val day4 = History("**********", "366", "46")
        val day5 = History("**********", "226", "32")
        val day6 = History("**********", "217", "42")
        val day7 = History("**********", "214", "34")

        assertEquals(expected = "nowplaying", actual = tag.name)
        assertEquals(expected = "https://mastodon.social/tags/nowplaying", actual = tag.url)
        assertEquals(
            expected =
                listOf(
                    day1,
                    day2,
                    day3,
                    day4,
                    day5,
                    day6,
                    day7,
                ),
            actual = tag.history,
        )
    }
}
