package com.amoretech.memory.data.network.model

import kotlinx.serialization.json.Json
import kotlin.test.Ignore
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class NotificationTests {
    // TODO: fix loading json from resources
    @Ignore
    @Test
    fun `deserialize required fields should succeed`() {
        // given
        // val json: String = javaClass.classLoader.getResource("response_notification_required.json").readText()
        val json: String = ""

        // when
        val notification = Json.decodeFromString<Notification>(json)

        // then
        assertNotNull(actual = notification)
        assertEquals(expected = "********", actual = notification.id)
        assertEquals(expected = NotificationType.MENTION, actual = notification.type)
        assertEquals(expected = "2019-11-23T07:49:02.064Z", actual = notification.createdAt)
        assertNotNull(actual = notification.account)
        assertEquals(expected = "23634", actual = notification.account.id)
        assertNotNull(actual = notification.status)
        assertEquals(expected = "103270115826048975", actual = notification.status.id)
    }
}
