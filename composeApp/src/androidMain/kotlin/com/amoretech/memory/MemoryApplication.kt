package com.amoretech.memory

import android.app.Application
import com.amoretech.memory.di.androidModule
import com.amoretech.memory.di.appModule
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin

class MemoryApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        startKoin {
            androidContext(this@MemoryApplication)
            androidLogger()
            printLogger()
            modules(appModule() + androidModule)
        }
    }
}
