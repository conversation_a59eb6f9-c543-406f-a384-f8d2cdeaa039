@file:Suppress("ktlint:standard:filename")

package com.amoretech.memory

import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import androidx.compose.ui.window.rememberWindowState
import com.amoretech.memory.di.appModule
import org.koin.core.context.startKoin

fun main() =
    application {
        startKoin {
            printLogger()
            modules(appModule())
        }

        val windowState = rememberWindowState()

        Window(
            onCloseRequest = ::exitApplication,
            state = windowState,
            title = "Memory",
        ) {
            App()
        }
    }
