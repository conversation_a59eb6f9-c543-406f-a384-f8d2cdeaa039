#Kotlin
kotlin.code.style=official
kotlin.daemon.jvmargs=-Xmx2048M

#Gradle
org.gradle.jvmargs=-Xmx2048M -Dfile.encoding=UTF-8

#Android
android.nonTransitiveRClass=true
android.useAndroidX=true

# To hide the following Kotlin/Native targets cannot be built on this machine and are disabled:
#iosArm64, iosSimulatorArm64, iosX64
kotlin.native.ignoreDisabledTargets=true

# To increased the compiler memory
kotlin.daemon.jvm.options=-Xmx2g
kotlin.compiler.execution.strategy=daemon

# to disable this warning
# If you use a static framework, Xcode should have Build Phase with copyFrameworkResourcesToApp gradle task call.
moko.resources.disableStaticFrameworkWarning=true

# Kotlin Multiplatform <-> Android Gradle Plugin compatibility issue:
# The applied Android Gradle Plugin version (8.9.0) is higher
# than the maximum known to the Kotlin Gradle Plugin.
# Tooling stability in such configuration isn't tested, please report encountered issues to https://kotl.in/issue
# kotlin.mpp.androidGradlePluginCompatibility.nowarn=true

org.gradle.java.home=/usr/lib/jvm/java-17-openjdk-amd64


