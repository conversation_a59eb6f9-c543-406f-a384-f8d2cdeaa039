package com.amoretech.memory.logging

import io.github.aakira.napier.Napier

object MemoryLogger {
    fun d(
        throwable: Throwable? = null,
        tag: String? = null,
        message: () -> String,
    ) {
        Napier.d(
            throwable = throwable,
            tag = tag,
            message = message,
        )
    }

    fun d(
        message: String,
        throwable: Throwable? = null,
        tag: String? = null,
    ) {
        Napier.d(
            message = message,
            throwable = throwable,
            tag = tag,
        )
    }

    fun i(
        throwable: Throwable? = null,
        tag: String? = null,
        message: () -> String,
    ) {
        Napier.i(
            throwable = throwable,
            tag = tag,
            message = message,
        )
    }

    fun i(
        message: String,
        throwable: Throwable? = null,
        tag: String? = null,
    ) {
        Napier.i(
            message = message,
            throwable = throwable,
            tag = tag,
        )
    }

    fun v(
        throwable: Throwable? = null,
        tag: String? = null,
        message: () -> String,
    ) {
        Napier.v(
            throwable = throwable,
            tag = tag,
            message = message,
        )
    }

    fun v(
        message: String,
        throwable: Throwable? = null,
        tag: String? = null,
    ) {
        Napier.v(
            message = message,
            throwable = throwable,
            tag = tag,
        )
    }

    fun w(
        throwable: Throwable? = null,
        tag: String? = null,
        message: () -> String,
    ) {
        Napier.w(
            throwable = throwable,
            tag = tag,
            message = message,
        )
    }

    fun w(
        message: String,
        throwable: Throwable? = null,
        tag: String? = null,
    ) {
        Napier.w(
            message = message,
            throwable = throwable,
            tag = tag,
        )
    }

    fun e(
        throwable: Throwable? = null,
        tag: String? = null,
        message: () -> String,
    ) {
        Napier.e(
            throwable = throwable,
            tag = tag,
            message = message,
        )
    }

    fun e(
        message: String,
        throwable: Throwable? = null,
        tag: String? = null,
    ) {
        Napier.e(
            message = message,
            throwable = throwable,
            tag = tag,
        )
    }

    fun assert(
        message: String,
        throwable: Throwable? = null,
        tag: String? = null,
    ) {
        Napier.wtf(
            message = message,
            throwable = throwable,
            tag = tag,
        )
    }

    fun assert(
        throwable: Throwable? = null,
        tag: String? = null,
        message: () -> String,
    ) {
        Napier.wtf(
            throwable = throwable,
            tag = tag,
            message = message,
        )
    }
}
