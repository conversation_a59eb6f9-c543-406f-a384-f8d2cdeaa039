import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.InvocationKind
import kotlin.contracts.contract

/**
 * Only return the result of [block] if [expression] is true, otherwise always returns null
 */
@OptIn(ExperimentalContracts::class)
inline fun <T> nullUnless(
    expression: <PERSON><PERSON><PERSON>,
    block: () -> T,
): T? {
    contract {
        returnsNotNull() implies (expression)
        callsInPlace(block, InvocationKind.AT_MOST_ONCE)
    }
    return if (!expression) {
        null
    } else {
        block()
    }
}
