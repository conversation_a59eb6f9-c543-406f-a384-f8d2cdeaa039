package com.amoretech.memory.login.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.login.model.LoginUserResult
import com.amoretech.memory.login.repository.LoginRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class LoginUserUseCase(
    private val loginRepository: LoginRepository,
) {
    fun loginUser(
        domain: String,
        clientId: String,
        clientSecret: String,
        code: String,
        redirectUri: String? = null,
    ): Flow<ApiResult<LoginUserResult>> =
        flow {
            // Step 1 to exchange code for token
            loginRepository
                .exchangeCodeForToken(domain, clientId, clientSecret, code, redirectUri)
                .collect { tokenResult ->
                    when (tokenResult) {
                        is ApiResult.Success -> {
                            val token = tokenResult.data
                            // Step 2 to verify credentials with access token
                            loginRepository
                                .verifyCredentials(domain, token.accessToken)
                                .collect { accountResult ->
                                    when (accountResult) {
                                        is ApiResult.Success -> {
                                            emit(
                                                ApiResult.Success(
                                                    LoginUserResult(
                                                        token,
                                                        accountResult.data,
                                                    ),
                                                ),
                                            )
                                        }

                                        is ApiResult.Error -> {
                                            emit(
                                                ApiResult.Error(
                                                    accountResult.message,
                                                    accountResult.cause,
                                                ),
                                            )
                                        }
                                    }
                                }
                        }

                        is ApiResult.Error -> {
                            emit(ApiResult.Error(tokenResult.message, tokenResult.cause))
                        }
                    }
                }
        }
}
