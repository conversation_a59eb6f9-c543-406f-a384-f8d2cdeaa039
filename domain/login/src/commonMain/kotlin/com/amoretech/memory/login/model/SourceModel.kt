package com.amoretech.memory.login.model

data class SourceModel(
    val privacy: String? = null,
    val sensitive: Boolean? = null,
    val language: String? = null,
    val note: String? = null,
    val fields: List<FieldModel> = emptyList(),
    val followRequestsCount: Int? = null,
    val hideCollections: Boolean? = null,
    val discoverable: Boolean? = null,
    val indexable: Boolean? = null,
    val attributionDomains: List<String> = emptyList(),
)
