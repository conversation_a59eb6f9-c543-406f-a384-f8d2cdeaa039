package com.amoretech.memory.login.repository

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.login.model.OAuthTokenModel
import com.amoretech.memory.login.model.UserAccountModel
import kotlinx.coroutines.flow.Flow

interface LoginRepository {
    fun buildAuthorizationUrl(
        domain: String,
        clientId: String,
        redirectUri: String? = null,
        scopes: String? = null,
    ): String

    fun exchangeCodeForToken(
        domain: String,
        clientId: String,
        clientSecret: String,
        code: String,
        redirectUri: String? = null,
    ): Flow<ApiResult<OAuthTokenModel>>

    suspend fun verifyCredentials(
        domain: String,
        accessToken: String,
    ): Flow<ApiResult<UserAccountModel>>
}
