package com.amoretech.memory.login.model

data class UserAccountModel(
    val id: String,
    val username: String,
    val acct: String,
    val displayName: String,
    val locked: Boolean,
    val bot: Boolean,
    val discoverable: Boolean? = null,
    val indexable: Boolean? = null,
    val group: Boolean? = null,
    val createdAt: String,
    val note: String,
    val url: String,
    val uri: String,
    val avatar: String,
    val avatarStatic: String,
    val header: String,
    val headerStatic: String,
    val followersCount: Int,
    val followingCount: Int,
    val statusesCount: Int,
    val lastStatusAt: String? = null,
    val hideCollections: Boolean? = null,
    val noindex: Boolean? = null,
    val source: SourceModel? = null,
    val emojis: List<EmojiModel> = emptyList(),
    val roles: List<RoleModel> = emptyList(),
    val fields: List<FieldModel> = emptyList(),
    val role: RoleModel? = null,
)
