package com.amoretech.memory.register.repository

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.model.AppRegistrationModel
import com.amoretech.memory.register.model.InstanceRuleModel
import com.amoretech.memory.register.model.TokenModel
import kotlinx.coroutines.flow.Flow

interface RegistrationRepository {
    fun getInstanceRules(domain: String): Flow<ApiResult<List<InstanceRuleModel>>>

    fun registerApp(domain: String): Flow<ApiResult<AppRegistrationModel>>

    suspend fun getAppToken(
        domain: String,
        clientId: String,
        clientSecret: String,
    ): Flow<ApiResult<TokenModel>>

    suspend fun createAccount(
        domain: String,
        appToken: String,
        userName: String,
        emailAddress: String,
        password: String,
        isAgreed: Boolean,
        localeString: String,
    ): Flow<ApiResult<TokenModel>>

    suspend fun checkUsernameAvailability(
        domain: String,
        username: String,
    ): Flow<ApiResult<Boolean>>

    suspend fun resendConfirmation(
        domain: String,
        userToken: String,
    ): Flow<ApiResult<Boolean>>
}
