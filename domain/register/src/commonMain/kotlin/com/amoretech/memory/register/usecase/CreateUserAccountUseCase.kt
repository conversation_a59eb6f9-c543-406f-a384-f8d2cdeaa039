package com.amoretech.memory.register.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.model.TokenModel
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow

class CreateUserAccountUseCase(
    private val registrationRepository: RegistrationRepository,
) {
    suspend operator fun invoke(
        domain: String,
        appToken: String,
        userName: String,
        emailAddress: String,
        password: String,
        locale: String = "en",
    ): Flow<ApiResult<TokenModel>> =
        registrationRepository.createAccount(
            domain = domain,
            appToken = appToken,
            userName = userName,
            emailAddress = emailAddress,
            password = password,
            isAgreed = true,
            localeString = locale,
        )
}
