package com.amoretech.memory.register.di

import com.amoretech.memory.register.usecase.CheckUsernameAvailabilityUseCase
import com.amoretech.memory.register.usecase.CreateUserAccountUseCase
import com.amoretech.memory.register.usecase.GetInstanceRulesUseCase
import com.amoretech.memory.register.usecase.ResendConfirmationUseCase
import org.koin.core.module.Module
import org.koin.dsl.module

val domainRegisterModule: Module =
    module {

        factory {
            GetInstanceRulesUseCase(
                registrationRepository = get(),
            )
        }

        factory {
            CheckUsernameAvailabilityUseCase(
                registrationRepository = get(),
            )
        }

        factory {
            CreateUserAccountUseCase(
                registrationRepository = get(),
            )
        }

        factory {
            ResendConfirmationUseCase(
                registrationRepository = get(),
            )
        }
    }
