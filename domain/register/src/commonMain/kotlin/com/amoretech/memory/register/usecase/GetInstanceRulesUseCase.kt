package com.amoretech.memory.register.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.model.InstanceRuleModel
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow

class GetInstanceRulesUseCase(
    private val registrationRepository: RegistrationRepository,
) {
    suspend operator fun invoke(domain: String): Flow<ApiResult<List<InstanceRuleModel>>> =
        registrationRepository.getInstanceRules(domain = domain)
}
