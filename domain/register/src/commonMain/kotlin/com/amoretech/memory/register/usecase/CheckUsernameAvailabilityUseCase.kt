package com.amoretech.memory.register.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow

class CheckUsernameAvailabilityUseCase(
    private val registrationRepository: RegistrationRepository,
) {
    suspend operator fun invoke(
        domain: String,
        username: String,
    ): Flow<ApiResult<Boolean>> = registrationRepository.checkUsernameAvailability(domain, username)
}
