package com.amoretech.memory.register.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow

class ResendConfirmationUseCase(
    private val registrationRepository: RegistrationRepository,
) {
    suspend operator fun invoke(
        domain: String,
        userToken: String,
    ): Flow<ApiResult<Boolean>> = registrationRepository.resendConfirmation(domain, userToken)
}
