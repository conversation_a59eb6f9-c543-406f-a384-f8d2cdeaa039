package com.amoretech.memory.serverlist.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.model.AppRegistrationModel
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow

class RegisterInstanceUseCase(
    private val registrationRepository: RegistrationRepository,
) {
    operator fun invoke(domain: String): Flow<ApiResult<AppRegistrationModel>> = registrationRepository.registerApp(domain)
}
