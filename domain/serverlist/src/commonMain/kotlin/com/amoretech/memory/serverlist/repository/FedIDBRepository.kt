package com.amoretech.memory.serverlist.repository

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.serverlist.model.AppTokenModel
import com.amoretech.memory.serverlist.model.FedIDBModel
import com.amoretech.memory.serverlist.model.InstanceRegistrationModel
import kotlinx.coroutines.flow.Flow

interface FedIDBRepository {
    fun getInstances(limit: Int): Flow<ApiResult<List<FedIDBModel>>>

    suspend fun checkHasInstanceRegister(domain: String): Boolean

    suspend fun saveInstanceRegistrationModel(model: InstanceRegistrationModel)

    suspend fun saveAccessToken(model: AppTokenModel)

    suspend fun getAccessTokenByDomain(domain: String): AppTokenModel?
}
