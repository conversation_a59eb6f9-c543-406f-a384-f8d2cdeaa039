package com.amoretech.memory.serverlist.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.register.model.TokenModel
import com.amoretech.memory.register.repository.RegistrationRepository
import kotlinx.coroutines.flow.Flow

class FetchAppTokenFromServerUseCase(
    private val registrationRepository: RegistrationRepository,
) {
    suspend operator fun invoke(
        domain: String,
        clientId: String,
        clientSecret: String,
    ): Flow<ApiResult<TokenModel>> = registrationRepository.getAppToken(domain, clientId, clientSecret)
}
