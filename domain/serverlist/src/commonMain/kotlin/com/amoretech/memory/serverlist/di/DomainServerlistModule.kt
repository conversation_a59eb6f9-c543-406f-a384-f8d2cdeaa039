package com.amoretech.memory.serverlist.di

import com.amoretech.memory.serverlist.usecase.CheckInstanceHasRegisterUseCase
import com.amoretech.memory.serverlist.usecase.FetchAppTokenFromServerUseCase
import com.amoretech.memory.serverlist.usecase.GetAccessTokenUseCase
import com.amoretech.memory.serverlist.usecase.GetFedIDBInstancesUseCase
import com.amoretech.memory.serverlist.usecase.RegisterInstanceUseCase
import com.amoretech.memory.serverlist.usecase.SaveAppTokenToDbUseCase
import com.amoretech.memory.serverlist.usecase.SaveInstanceRegistrationUseCase
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin module containing all koin/bean definitions for
 * domain related classes to fetch the server list
 */
val domainServerListModule: Module =
    module {

        factory {
            GetFedIDBInstancesUseCase(
                repository = get(),
            )
        }

        factory {
            CheckInstanceHasRegisterUseCase(
                repository = get(),
            )
        }

        factory {
            SaveInstanceRegistrationUseCase(
                repository = get(),
            )
        }

        factory {
            RegisterInstanceUseCase(
                registrationRepository = get(),
            )
        }

        factory {
            FetchAppTokenFromServerUseCase(
                registrationRepository = get(),
            )
        }

        factory {
            SaveAppTokenToDbUseCase(
                repository = get(),
            )
        }

        factory {
            GetAccessTokenUseCase(
                repository = get(),
            )
        }
    }
