package com.amoretech.memory.serverlist.usecase

import com.amoretech.memory.kotlinUtils.ApiResult
import com.amoretech.memory.serverlist.model.FedIDBModel
import com.amoretech.memory.serverlist.repository.FedIDBRepository
import kotlinx.coroutines.flow.Flow

class GetFedIDBInstancesUseCase(
    private val repository: FedIDBRepository,
) {
    suspend operator fun invoke(limit: Int = 10): Flow<ApiResult<List<FedIDBModel>>> = repository.getInstances(limit)
}
