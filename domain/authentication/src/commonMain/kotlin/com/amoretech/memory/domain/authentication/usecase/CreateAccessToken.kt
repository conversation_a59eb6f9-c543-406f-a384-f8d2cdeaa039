package com.amoretech.memory.domain.authentication.usecase

import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository

class CreateAccessToken(
    private val authenticationRepository: AuthenticationRepository,
) {
    suspend operator fun invoke(
        authCode: String,
        server: String,
    ): Bo<PERSON>an {
        val token =
            authenticationRepository.createAccessToken(
                server = server,
                authCode = authCode,
                scope = "read write follow push",
                grantType = "authorization_code",
            )

        return if (token != null) {
            authenticationRepository.saveAccessToken(server = server, token = token)
            true
        } else {
            false
        }
    }
}
