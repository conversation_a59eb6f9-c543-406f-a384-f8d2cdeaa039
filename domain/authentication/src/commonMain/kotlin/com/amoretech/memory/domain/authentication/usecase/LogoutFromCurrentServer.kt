package com.amoretech.memory.domain.authentication.usecase

import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository

class LogoutFromCurrentServer(
    private val authenticationRepository: AuthenticationRepository,
) {
    operator fun invoke() {
        val server = authenticationRepository.selectedServer
        if (server != null) {
            authenticationRepository.removeAccessToken(server)
        }
    }
}
