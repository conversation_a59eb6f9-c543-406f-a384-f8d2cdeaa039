package com.amoretech.memory.domain.authentication.usecase

import com.amoretech.memory.domain.authentication.model.AuthStatus
import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class GetAuthStatus(
    private val authenticationRepository: AuthenticationRepository,
) {
    operator fun invoke(): Flow<AuthStatus> =
        authenticationRepository.isAccessTokenPresent().map { hasAccessToken ->
            if (hasAccessToken) {
                AuthStatus.Authorized
            } else {
                AuthStatus.Unauthorized
            }
        }
}
