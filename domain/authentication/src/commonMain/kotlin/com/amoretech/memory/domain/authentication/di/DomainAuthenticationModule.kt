package com.amoretech.memory.domain.authentication.di

import com.amoretech.memory.domain.authentication.usecase.AuthenticateClient
import com.amoretech.memory.domain.authentication.usecase.CreateAccessToken
import com.amoretech.memory.domain.authentication.usecase.GetAuthStatus
import com.amoretech.memory.domain.authentication.usecase.GetSelectedApplicationOAuthToken
import com.amoretech.memory.domain.authentication.usecase.LogoutFromCurrentServer
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * Koin module containing all koin/bean definitions for
 * domain related classes for authentication
 */
val domainAuthModule: Module =
    module {

        factory {
            AuthenticateClient(
                authenticationRepository = get(),
            )
        }

        factory {
            GetSelectedApplicationOAuthToken(
                authenticationRepository = get(),
            )
        }

        factory {
            CreateAccessToken(
                authenticationRepository = get(),
            )
        }

        factory {
            GetAuthStatus(
                authenticationRepository = get(),
            )
        }

        factory {
            LogoutFromCurrentServer(
                authenticationRepository = get(),
            )
        }
    }
