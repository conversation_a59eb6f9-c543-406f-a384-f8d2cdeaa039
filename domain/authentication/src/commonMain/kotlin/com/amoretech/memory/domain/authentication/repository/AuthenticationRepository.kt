package com.amoretech.memory.domain.authentication.repository

import com.amoretech.memory.domain.authentication.model.ApplicationOAuthToken
import com.amoretech.memory.domain.authentication.model.NewAppOAuthToken
import kotlinx.coroutines.flow.Flow

interface AuthenticationRepository {
    suspend fun createApplicationClient(
        domain: String,
        clientName: String,
        redirectUris: String,
        scopes: String,
        website: String?,
    ): NewAppOAuthToken?

    suspend fun saveApplication(
        token: NewAppOAuthToken,
        domain: String,
    )

    suspend fun createAccessToken(
        authCode: String,
        server: String,
        scope: String,
        grantType: String,
    ): String?

    suspend fun saveAccessToken(
        server: String,
        token: String,
    )

    val selectedServer: String?

    suspend fun getApplicationOAuthToken(server: String): ApplicationOAuthToken?

    fun isAccessTokenPresent(): Flow<Boolean>

    fun removeAccessToken(server: String)

    /**
     * Get current user's access token for the selected server
     */
    fun getCurrentAccessToken(): String?

    /**
     * Verify that the current access token is still valid
     */
    suspend fun verifyCurrentCredentials(): <PERSON><PERSON><PERSON>
}
