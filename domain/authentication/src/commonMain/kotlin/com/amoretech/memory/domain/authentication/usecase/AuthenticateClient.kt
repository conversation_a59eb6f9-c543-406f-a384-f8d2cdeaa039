package com.amoretech.memory.domain.authentication.usecase

import com.amoretech.memory.domain.authentication.repository.AuthenticationRepository

class AuthenticateClient(
    private val authenticationRepository: AuthenticationRepository,
) {
    suspend operator fun invoke(
        domain: String,
        clientName: String,
        redirectURIs: String,
        scopes: String,
        website: String? = null,
    ): Boolean {
        val oAuthToken =
            authenticationRepository.createApplicationClient(
                domain = domain,
                clientName = clientName,
                redirectUris = redirectURIs,
                scopes = scopes,
                website = website,
            )

        return if (oAuthToken != null) {
            authenticationRepository.saveApplication(
                token = oAuthToken,
                domain = domain,
            )
            true
        } else {
            false
        }
    }
}
