package com.amoretech.memory.domain.timeline.repository

import com.amoretech.memory.domain.timeline.model.StatusLocal
import kotlinx.coroutines.flow.Flow
import org.mobilenativefoundation.store.store5.StoreReadResponse

interface HomeTimelineRepository {
    fun read(
        feedType: FeedType,
        refresh: Boolean = false,
    ): Flow<StoreReadResponse<List<StatusLocal>>>
}

sealed class FeedType(
    val type: String,
) {
    data object Home : FeedType("HOME")
}
