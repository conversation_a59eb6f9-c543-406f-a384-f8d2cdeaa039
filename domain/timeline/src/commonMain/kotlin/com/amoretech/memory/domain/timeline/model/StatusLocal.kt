package com.amoretech.memory.domain.timeline.model

import com.amoretech.memory.domain.timeline.repository.FeedType

data class StatusLocal(
    val remoteId: String,
    val feedType: FeedType,
    val createdAt: String,
    val repliesCount: Long = 0,
    val reblogsCount: Long = 0,
    val favoritesCount: Long = 0,
    val content: String,
    val account: Account? = null,
    val sensitive: Boolean = false,
    val spoilerText: String? = null,
    val visibility: String,
    val avatarUrl: String = "",
    val accountAddress: String = "",
    val userName: String,
)
