package com.amoretech.memory.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.amoretech.memory.landing.view.LandingScreen
import com.amoretech.memory.serverselect.view.SelectServerScreen
import com.amoretech.memory.ui.common.screen.AppScreen
import com.amoretech.memory.ui.root.splash.SplashScreen
import com.amoretech.memory.ui.settings.SettingsScreen
import com.amoretech.memory.ui.signedIn.SignInScreen
import com.amoretech.memory.ui.timeline.TimelineScreen

@Suppress("ktlint:standard:function-naming")
/**
 * Main app navigation with back gesture support
 */
@Composable
fun AppNavigation() {
    val navController = rememberNavController()

    NavHost(
        navController = navController,
        startDestination = AppScreen.Splash,
    ) {
        composable<AppScreen.Splash> {
            SplashScreen(
                navigateToLanding = {
                    navController.navigate(AppScreen.Landing)
                },
                navigateToSignIn = {},
            )
        }

        composable<AppScreen.Landing> {
            LandingScreen(
                onNavigateToSelectServer = {
                    navController.navigate(AppScreen.SelectServer)
                },
                onNavigateToSignIn = {
                    navController.navigate(
                        AppScreen.SignIn(
                            server = "test server",
                            oauthUrl = "oauth url",
                            redirectUri = "redirectUri",
                        ),
                    )
                },
            )
        }

        composable<AppScreen.SelectServer> {
            SelectServerScreen(
                onNavigateToSignIn = { server, oauthUrl, redirectUri ->
                    navController.navigate(AppScreen.SignIn(server, oauthUrl, redirectUri))
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
                navigateToCreateAccount = {
                    // Navigate to create account
                },
            )
        }

        composable<AppScreen.SignIn> {
            // todo remove the params at screen, they should pass to content
            SignInScreen(
                //  server = screen.server,
                server = "test server",
                //  oauthUrl = screen.oauthUrl,
                oauthUrl = "oauth url",
                //  redirectUri = screen.redirectUri,
                redirectUri = "redirectUri",
                onNavigateToTimeline = {
                    navController.navigate(AppScreen.Timeline)
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.Settings> {
            SettingsScreen(
                onNavigateToLanding = {
                    // todo
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.Timeline> {
            TimelineScreen(
                onNavigateToSettings = {
                    navController.navigate(AppScreen.Settings)
                },
            )
        }
    }
}
