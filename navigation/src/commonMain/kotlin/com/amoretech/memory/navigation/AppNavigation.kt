package com.amoretech.memory.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import com.amoretech.memory.landing.view.LandingScreen
import com.amoretech.memory.serverselect.view.RuleScreen
import com.amoretech.memory.serverselect.view.SelectServerScreen
import com.amoretech.memory.ui.common.screen.AppScreen
import com.amoretech.memory.ui.createaccount.view.CreateAccountScreen
import com.amoretech.memory.ui.oobverification.view.OobVerificationScreen
import com.amoretech.memory.ui.root.splash.SplashScreen
import com.amoretech.memory.ui.settings.SettingsScreen
import com.amoretech.memory.ui.signedIn.SignInScreen
import com.amoretech.memory.ui.timeline.TimelineScreen

@Suppress("ktlint:standard:function-naming")
/**
 * Main app navigation with back gesture support
 */
@Composable
fun AppNavigation() {
    val navController = rememberNavController()

    NavHost(
        navController = navController,
        startDestination = AppScreen.Splash,
    ) {
        composable<AppScreen.Splash> {
            SplashScreen(
                navigateToLanding = {
                    navController.navigate(AppScreen.Landing)
                },
                navigateToTimeline = {
                    navController.navigate(AppScreen.Timeline)
                },
                navigateToOobVerification = { domain, email ->
                    navController.navigate(AppScreen.OobVerification(domain = domain, email = email))
                },
                navigateToSignIn = { domain ->
                    navController.navigate(AppScreen.SignIn(
                        server = domain,
                        oauthUrl = "oauth url", // TODO: Generate proper OAuth URL
                        redirectUri = "redirectUri" // TODO: Use proper redirect URI
                    ))
                },
            )
        }

        composable<AppScreen.Landing> {
            LandingScreen(
                onNavigateToSelectServer = {
                    navController.navigate(AppScreen.SelectServer)
                },
                onNavigateToSignIn = {
                    navController.navigate(
                        AppScreen.SignIn(
                            server = "test server",
                            oauthUrl = "oauth url",
                            redirectUri = "redirectUri",
                        ),
                    )
                },
            )
        }

        composable<AppScreen.SelectServer> {
            SelectServerScreen(
                onNavigateToSignIn = { server, oauthUrl, redirectUri ->
                    navController.navigate(AppScreen.SignIn(server, oauthUrl, redirectUri))
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
                navigateToRule = { domain ->
                    navController.navigate(AppScreen.Rules(domain))
                },
            )
        }

        composable<AppScreen.Rules> { backStackEntry ->
            val data = backStackEntry.toRoute<AppScreen.Rules>()

            RuleScreen(
                domain = data.domain,
                navigateToWebView = { domain ->
                    navController.navigate(AppScreen.WebViewScreen(domain))
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.WebViewScreen> { backStackEntry ->
            val data = backStackEntry.toRoute<AppScreen.WebViewScreen>()
            // TODO add the webview
//            RuleScreen(
//                domain = data.url,
//                navigateToWebView = {domain ->
//                    navController.navigate(AppScreen.WebViewScreen(domain))
//                },
//                onNavigateBack = {
//                    navController.navigateUp()
//                },
//            )
        }

        composable<AppScreen.CreateAccount> { backStackEntry ->
            val data = backStackEntry.toRoute<AppScreen.CreateAccount>()
            CreateAccountScreen(
                domain = data.domain,
                navigateToOobCodeScreen = { email ->
                    navController.navigate(AppScreen.OobVerification(domain = data.domain, email = email))
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.OobVerification> { backStackEntry ->
            val data = backStackEntry.toRoute<AppScreen.OobVerification>()
            OobVerificationScreen(
                domain = data.domain,
                email = data.email,
                onNavigateToSignIn = {
                    // Navigate to sign in with the verified domain
                    navController.navigate(AppScreen.SignIn(
                        server = data.domain,
                        oauthUrl = "oauth url", // TODO: Generate proper OAuth URL
                        redirectUri = "redirectUri" // TODO: Use proper redirect URI
                    ))
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.SignIn> {
            // todo remove the params at screen, they should pass to content
            SignInScreen(
                //  server = screen.server,
                server = "test server",
                //  oauthUrl = screen.oauthUrl,
                oauthUrl = "oauth url",
                //  redirectUri = screen.redirectUri,
                redirectUri = "redirectUri",
                onNavigateToTimeline = {
                    navController.navigate(AppScreen.Timeline)
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.Settings> {
            SettingsScreen(
                onNavigateToLanding = {
                    // todo
                },
                onNavigateBack = {
                    navController.navigateUp()
                },
            )
        }

        composable<AppScreen.Timeline> {
            TimelineScreen(
                onNavigateToSettings = {
                    navController.navigate(AppScreen.Settings)
                },
            )
        }
    }
}
