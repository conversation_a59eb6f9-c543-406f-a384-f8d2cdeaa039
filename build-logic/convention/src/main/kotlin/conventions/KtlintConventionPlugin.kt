package conventions

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.jlleitschuh.gradle.ktlint.KtlintExtension
import org.jlleitschuh.gradle.ktlint.tasks.KtLintCheckTask
import org.jlleitschuh.gradle.ktlint.tasks.KtLintFormatTask

class KtlintConventionPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        project.pluginManager.apply("org.jlleitschuh.gradle.ktlint")

        project.extensions.configure(KtlintExtension::class.java) {
            version.set("1.5.0")
            enableExperimentalRules.set(true)
            verbose.set(true)
            outputToConsole.set(true)
            ignoreFailures.set(!System.getenv("CI").toBoolean())
            filter {
                exclude("**/generated/**")
            }
        }

        project.tasks.withType(KtLintCheckTask::class.java).configureEach {
            group = "verification"
            description = "Runs ktlint checks"
        }
        project.tasks.withType(KtLintFormatTask::class.java).configureEach {
            group = "formatting"
            description = "Formats Kotlin code using ktlint"
        }
    }
}
