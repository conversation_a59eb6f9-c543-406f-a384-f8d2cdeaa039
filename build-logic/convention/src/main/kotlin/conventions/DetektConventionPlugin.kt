package conventions

import io.gitlab.arturbosch.detekt.Detekt
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType

class DetektConventionPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        project.plugins.apply("io.gitlab.arturbosch.detekt")

        project.dependencies {
            add("detektPlugins", "io.gitlab.arturbosch.detekt:detekt-formatting:1.23.8")
        }

        project.extensions.configure(io.gitlab.arturbosch.detekt.extensions.DetektExtension::class.java) {
            config.setFrom(project.rootProject.file("detekt.yml"))
            buildUponDefaultConfig = true
            parallel = true
            autoCorrect = false
            ignoreFailures = !System.getenv("CI").toBoolean()
        }

        project.tasks.withType<Detekt>().configureEach {
            reports {
                html.required.set(true)
                xml.required.set(false)
                txt.required.set(false)
                sarif.required.set(false)
            }
        }
    }
}
