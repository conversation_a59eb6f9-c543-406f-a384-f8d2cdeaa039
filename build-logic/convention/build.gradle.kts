@Suppress("DSL_SCOPE_VIOLATION") // https://youtrack.jetbrains.com/issue/KTIJ-19369

plugins {
    `kotlin-dsl`
}

repositories {
    mavenCentral()
    gradlePluginPortal()
}

dependencies {
    implementation("io.gitlab.arturbosch.detekt:detekt-gradle-plugin:1.23.8")
    implementation("io.gitlab.arturbosch.detekt:detekt-formatting:1.23.8")
    implementation("org.jlleitschuh.gradle:ktlint-gradle:12.2.0")
}

gradlePlugin {
    plugins {
        register("detektConvention") {
            id = "detekt"
            implementationClass = "conventions.DetektConventionPlugin"
        }
        register("ktlintConvention") {
            id = "ktlint"
            implementationClass = "conventions.KtlintConventionPlugin"
        }
    }
}
